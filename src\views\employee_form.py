"""
نموذج إضافة/تعديل الموظف
Employee Form
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLineEdit, QComboBox,
    QDateEdit, QTextEdit, QPushButton, QLabel, QFrame, QGroupBox, QMessageBox
)
from PySide6.QtCore import Qt, QDate, Signal
from PySide6.QtGui import QIntValidator

from ..models import Employee, Department, JobTitle, EmploymentStatus
from ..database import get_db_session_context
from ..utils import (
    show_message, apply_rtl_layout, clean_numeric_input, format_currency,
    generate_employee_number
)


class EmployeeForm(QDialog):
    """نموذج إضافة/تعديل الموظف"""
    
    # الإشارات
    employee_saved = Signal(int)
    
    def __init__(self, employee_id: int = None, parent=None):
        super().__init__(parent)
        self.employee_id = employee_id
        self.is_edit_mode = employee_id is not None
        self.employee = None
        
        self.setup_ui()
        self.setup_connections()
        self.load_data()
        
        if self.is_edit_mode:
            self.load_employee_data()
        else:
            self.generate_employee_number()
            
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        apply_rtl_layout(self)
        
        # إعداد النافذة
        title = "✏️ تعديل الموظف" if self.is_edit_mode else "➕ إضافة موظف جديد"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(1200, 850)  # حجم أكبر لحل مشكلة التخربط
        self.setMinimumSize(1150, 800)  # حد أدنى أكبر
        
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(25, 25, 25, 25)
        main_layout.setSpacing(20)
        
        # إنشاء رأس النموذج
        self.create_header(main_layout)
        
        # إنشاء مجموعات الحقول
        self.create_personal_info_group(main_layout)
        self.create_job_info_group(main_layout)
        
        # إنشاء أزرار الإجراءات
        self.create_action_buttons(main_layout)
        
    def create_header(self, layout: QVBoxLayout):
        """إنشاء رأس النموذج"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                            stop: 0 #667eea, stop: 1 #764ba2);
                border: none;
                border-radius: 16px;
                margin: 12px;
            }
        """)
        
        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(20, 15, 20, 15)
        header_layout.setSpacing(5)
        
        # العنوان الرئيسي
        title = "✏️ تعديل بيانات الموظف" if self.is_edit_mode else "➕ إضافة موظف جديد"
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 24px;
                font-weight: bold;
                color: white;
                margin: 8px 0px;
            }
        """)
        
        # العنوان الفرعي
        subtitle_text = "تعديل وتحديث بيانات الموظف" if self.is_edit_mode else "إدخال بيانات الموظف الجديد"
        subtitle_label = QLabel(subtitle_text)
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                color: rgba(255, 255, 255, 0.9);
                margin: 4px 0px;
            }
        """)
        
        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)
        
        layout.addWidget(header_frame)
        
    def create_personal_info_group(self, layout: QVBoxLayout):
        """إنشاء مجموعة المعلومات الشخصية"""
        group = QGroupBox("👤 المعلومات الشخصية")
        group.setStyleSheet("""
            QGroupBox {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #3498db;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 5px 10px;
                background-color: #3498db;
                color: white;
                border-radius: 5px;
            }
        """)
        
        group_layout = QFormLayout(group)
        group_layout.setSpacing(15)
        group_layout.setContentsMargins(20, 25, 20, 20)
        
        # أنماط الحقول
        input_style = """
            QLineEdit, QDateEdit, QTextEdit {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                padding: 10px;
                border: 2px solid #dee2e6;
                border-radius: 6px;
                background-color: #f8f9fa;
                min-height: 20px;
            }
            QLineEdit:focus, QDateEdit:focus, QTextEdit:focus {
                border-color: #3498db;
                background-color: white;
            }
            QLineEdit:hover, QDateEdit:hover, QTextEdit:hover {
                border-color: #adb5bd;
            }
        """
        
        label_style = """
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                font-weight: 600;
                color: #495057;
                padding: 5px 0px;
            }
        """
        
        # الرقم الوظيفي
        self.employee_number_input = QLineEdit()
        self.employee_number_input.setMaxLength(20)
        self.employee_number_input.setStyleSheet(input_style)
        self.employee_number_input.setPlaceholderText("مثال: EMP001")
        emp_label = QLabel("🆔 الرقم الوظيفي *:")
        emp_label.setStyleSheet(label_style)
        group_layout.addRow(emp_label, self.employee_number_input)
        
        # الاسم الكامل
        self.full_name_input = QLineEdit()
        self.full_name_input.setMaxLength(200)
        self.full_name_input.setStyleSheet(input_style)
        self.full_name_input.setPlaceholderText("الاسم الثلاثي أو الرباعي")
        name_label = QLabel("👤 الاسم الكامل *:")
        name_label.setStyleSheet(label_style)
        group_layout.addRow(name_label, self.full_name_input)
        
        # تاريخ الميلاد
        self.birth_date_input = QDateEdit()
        self.birth_date_input.setDate(QDate.currentDate().addYears(-25))
        self.birth_date_input.setMaximumDate(QDate.currentDate().addYears(-18))
        self.birth_date_input.setCalendarPopup(True)
        self.birth_date_input.setStyleSheet(input_style)
        birth_label = QLabel("📅 تاريخ الميلاد:")
        birth_label.setStyleSheet(label_style)
        group_layout.addRow(birth_label, self.birth_date_input)
        
        # الجنسية
        self.nationality_input = QLineEdit()
        self.nationality_input.setMaxLength(50)
        self.nationality_input.setText("عراقي")
        self.nationality_input.setStyleSheet(input_style)
        nationality_label = QLabel("🌍 الجنسية:")
        nationality_label.setStyleSheet(label_style)
        group_layout.addRow(nationality_label, self.nationality_input)
        
        # رقم الهاتف
        self.phone_input = QLineEdit()
        self.phone_input.setMaxLength(20)
        self.phone_input.setPlaceholderText("07901234567")
        self.phone_input.setStyleSheet(input_style)
        phone_label = QLabel("📞 رقم الهاتف:")
        phone_label.setStyleSheet(label_style)
        group_layout.addRow(phone_label, self.phone_input)
        
        # العنوان
        self.address_input = QTextEdit()
        self.address_input.setMaximumHeight(80)
        self.address_input.setStyleSheet(input_style)
        self.address_input.setPlaceholderText("العنوان التفصيلي")
        address_label = QLabel("🏠 العنوان:")
        address_label.setStyleSheet(label_style)
        group_layout.addRow(address_label, self.address_input)
        
        layout.addWidget(group)

    def create_job_info_group(self, layout: QVBoxLayout):
        """إنشاء مجموعة المعلومات الوظيفية"""
        group = QGroupBox("💼 المعلومات الوظيفية")
        group.setStyleSheet("""
            QGroupBox {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #27ae60;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 5px 10px;
                background-color: #27ae60;
                color: white;
                border-radius: 5px;
            }
        """)

        group_layout = QFormLayout(group)
        group_layout.setSpacing(15)
        group_layout.setContentsMargins(20, 25, 20, 20)

        # أنماط الحقول
        input_style = """
            QLineEdit, QDateEdit, QComboBox {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                padding: 10px;
                border: 2px solid #dee2e6;
                border-radius: 6px;
                background-color: #f8f9fa;
                min-height: 20px;
            }
            QLineEdit:focus, QDateEdit:focus, QComboBox:focus {
                border-color: #27ae60;
                background-color: white;
            }
            QLineEdit:hover, QDateEdit:hover, QComboBox:hover {
                border-color: #adb5bd;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #6c757d;
                margin-right: 10px;
            }
        """

        label_style = """
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                font-weight: 600;
                color: #495057;
                padding: 5px 0px;
            }
        """

        # تاريخ المباشرة
        self.hire_date_input = QDateEdit()
        self.hire_date_input.setDate(QDate.currentDate())
        self.hire_date_input.setMaximumDate(QDate.currentDate())
        self.hire_date_input.setCalendarPopup(True)
        self.hire_date_input.setStyleSheet(input_style)
        hire_label = QLabel("📅 تاريخ المباشرة *:")
        hire_label.setStyleSheet(label_style)
        group_layout.addRow(hire_label, self.hire_date_input)

        # القسم
        self.department_combo = QComboBox()
        self.department_combo.setStyleSheet(input_style)
        dept_label = QLabel("🏢 القسم *:")
        dept_label.setStyleSheet(label_style)
        group_layout.addRow(dept_label, self.department_combo)

        # العنوان الوظيفي
        self.job_title_combo = QComboBox()
        self.job_title_combo.setStyleSheet(input_style)
        job_label = QLabel("💼 العنوان الوظيفي *:")
        job_label.setStyleSheet(label_style)
        group_layout.addRow(job_label, self.job_title_combo)

        # الراتب الأساسي
        self.basic_salary_input = QLineEdit()
        self.basic_salary_input.setPlaceholderText("1,000,000 (دينار عراقي)")
        self.basic_salary_input.setStyleSheet(input_style)
        # ربط دالة تنسيق الراتب
        self.basic_salary_input.textChanged.connect(self.format_salary_input)
        salary_label = QLabel("💰 الراتب الأساسي *:")
        salary_label.setStyleSheet(label_style)
        group_layout.addRow(salary_label, self.basic_salary_input)

        # حالة التوظيف
        self.employment_status_combo = QComboBox()
        self.employment_status_combo.setStyleSheet(input_style)
        status_label = QLabel("📊 حالة التوظيف *:")
        status_label.setStyleSheet(label_style)
        group_layout.addRow(status_label, self.employment_status_combo)

        layout.addWidget(group)

    def create_action_buttons(self, layout: QVBoxLayout):
        """إنشاء أزرار الإجراءات"""
        # إطار الأزرار
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 10px;
                padding: 15px;
                margin: 10px 0px;
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(15)

        # زر الحفظ
        save_text = "✏️ تحديث البيانات" if self.is_edit_mode else "💾 حفظ الموظف"
        self.save_btn = QPushButton(save_text)
        self.save_btn.setMinimumWidth(150)
        self.save_btn.setMinimumHeight(45)
        self.save_btn.setStyleSheet("""
            QPushButton {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 16px;
                font-weight: bold;
                color: white;
                background-color: #28a745;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
            }
            QPushButton:hover {
                background-color: #218838;
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background-color: #1e7e34;
                transform: translateY(0px);
            }
        """)

        # زر المسح (للنموذج الجديد فقط)
        if not self.is_edit_mode:
            self.clear_btn = QPushButton("🗑️ مسح الحقول")
            self.clear_btn.setMinimumWidth(130)
            self.clear_btn.setMinimumHeight(45)
            self.clear_btn.setStyleSheet("""
                QPushButton {
                    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                    font-size: 16px;
                    font-weight: bold;
                    color: white;
                    background-color: #ffc107;
                    border: none;
                    border-radius: 8px;
                    padding: 12px 20px;
                }
                QPushButton:hover {
                    background-color: #e0a800;
                    transform: translateY(-2px);
                }
                QPushButton:pressed {
                    background-color: #d39e00;
                    transform: translateY(0px);
                }
            """)
            self.clear_btn.clicked.connect(self.clear_form)

        # زر الإلغاء
        self.cancel_btn = QPushButton("❌ إلغاء")
        self.cancel_btn.setMinimumWidth(120)
        self.cancel_btn.setMinimumHeight(45)
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 16px;
                font-weight: bold;
                color: white;
                background-color: #dc3545;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
            }
            QPushButton:hover {
                background-color: #c82333;
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background-color: #bd2130;
                transform: translateY(0px);
            }
        """)

        # ترتيب الأزرار
        buttons_layout.addStretch()
        if not self.is_edit_mode:
            buttons_layout.addWidget(self.clear_btn)
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.cancel_btn)
        buttons_layout.addStretch()

        layout.addWidget(buttons_frame)

    def setup_connections(self):
        """إعداد الاتصالات"""
        self.save_btn.clicked.connect(self.save_employee)
        self.cancel_btn.clicked.connect(self.reject)

        # ربط تغيير العنوان الوظيفي بتحديث نطاق الراتب
        self.job_title_combo.currentTextChanged.connect(self.update_salary_range)

    def load_data(self):
        """تحميل البيانات الأساسية"""
        try:
            with get_db_session_context() as session:
                # تحميل الأقسام
                departments = session.query(Department).filter_by(is_active=True).all()
                self.department_combo.clear()
                self.department_combo.addItem("-- اختر القسم --", None)
                for dept in departments:
                    self.department_combo.addItem(dept.name, dept.id)

                # تحميل العناوين الوظيفية
                job_titles = session.query(JobTitle).filter_by(is_active=True).all()
                self.job_title_combo.clear()
                self.job_title_combo.addItem("-- اختر العنوان الوظيفي --", None)
                for title in job_titles:
                    self.job_title_combo.addItem(title.title, title.id)

                # تحميل حالات التوظيف
                self.employment_status_combo.clear()
                for status in EmploymentStatus:
                    self.employment_status_combo.addItem(status.value, status)

        except Exception as e:
            show_message(self, "خطأ", f"فشل في تحميل البيانات: {e}", "error")

    def generate_employee_number(self):
        """توليد رقم وظيفي جديد"""
        try:
            with get_db_session_context() as session:
                # الحصول على آخر رقم وظيفي
                last_employee = session.query(Employee).order_by(Employee.id.desc()).first()
                last_number = last_employee.employee_number if last_employee else None

                # توليد رقم جديد
                new_number = generate_employee_number(last_number)
                self.employee_number_input.setText(new_number)

        except Exception as e:
            show_message(self, "تحذير", f"فشل في توليد الرقم الوظيفي: {e}", "warning")

    def clear_form(self):
        """مسح جميع حقول النموذج"""
        # مسح الحقول النصية
        self.employee_number_input.clear()
        self.full_name_input.clear()
        self.nationality_input.setText("عراقي")
        self.phone_input.clear()
        self.address_input.clear()
        self.basic_salary_input.clear()

        # إعادة تعيين التواريخ
        self.birth_date_input.setDate(QDate.currentDate().addYears(-25))
        self.hire_date_input.setDate(QDate.currentDate())

        # إعادة تعيين القوائم المنسدلة
        self.department_combo.setCurrentIndex(0)
        self.job_title_combo.setCurrentIndex(0)
        self.employment_status_combo.setCurrentIndex(0)

        # توليد رقم وظيفي جديد
        self.generate_employee_number()

    def format_salary_input(self):
        """تنسيق إدخال الراتب بالفواصل"""
        text = self.basic_salary_input.text()
        # إزالة الفواصل الموجودة
        clean_text = text.replace(',', '').replace('.', '')

        if clean_text.isdigit() and clean_text:
            # تنسيق الرقم بالفواصل
            formatted = format_currency(int(clean_text), show_currency=False)

            # تحديث النص مع الحفاظ على موضع المؤشر
            cursor_pos = self.basic_salary_input.cursorPosition()
            self.basic_salary_input.setText(formatted)

            # تعديل موضع المؤشر
            new_pos = min(cursor_pos, len(formatted))
            self.basic_salary_input.setCursorPosition(new_pos)

    def update_salary_range(self):
        """تحديث نطاق الراتب حسب العنوان الوظيفي"""
        job_title_id = self.job_title_combo.currentData()
        if not job_title_id:
            return

        try:
            with get_db_session_context() as session:
                job_title = session.query(JobTitle).filter_by(id=job_title_id).first()
                if job_title and job_title.min_salary and job_title.max_salary:
                    min_salary = format_currency(job_title.min_salary, show_currency=False)
                    max_salary = format_currency(job_title.max_salary, show_currency=False)
                    placeholder = f"نطاق الراتب: {min_salary} - {max_salary} دينار"
                    self.basic_salary_input.setPlaceholderText(placeholder)

        except Exception as e:
            print(f"خطأ في تحديث نطاق الراتب: {e}")

    def validate_form(self) -> bool:
        """التحقق من صحة البيانات"""
        # التحقق من الحقول المطلوبة
        if not self.employee_number_input.text().strip():
            show_message(self, "تحذير", "الرقم الوظيفي مطلوب", "warning")
            self.employee_number_input.setFocus()
            return False

        if not self.full_name_input.text().strip():
            show_message(self, "تحذير", "الاسم الكامل مطلوب", "warning")
            self.full_name_input.setFocus()
            return False

        if not self.department_combo.currentData():
            show_message(self, "تحذير", "يرجى اختيار القسم", "warning")
            self.department_combo.setFocus()
            return False

        if not self.job_title_combo.currentData():
            show_message(self, "تحذير", "يرجى اختيار العنوان الوظيفي", "warning")
            self.job_title_combo.setFocus()
            return False

        # التحقق من الراتب
        salary_text = self.basic_salary_input.text().strip()
        if not salary_text:
            show_message(self, "تحذير", "الراتب الأساسي مطلوب", "warning")
            self.basic_salary_input.setFocus()
            return False

        salary = clean_numeric_input(salary_text)
        if not salary or salary <= 0:
            show_message(self, "تحذير", "يرجى إدخال راتب صحيح", "warning")
            self.basic_salary_input.setFocus()
            return False

        return True

    def save_employee(self):
        """حفظ بيانات الموظف"""
        if not self.validate_form():
            return

        try:
            with get_db_session_context() as session:
                # إنشاء أو تحديث الموظف
                if self.is_edit_mode:
                    employee = session.query(Employee).filter_by(id=self.employee_id).first()
                    if not employee:
                        show_message(self, "خطأ", "الموظف غير موجود", "error")
                        return
                else:
                    employee = Employee()
                    session.add(employee)

                # تعيين البيانات
                employee.employee_number = self.employee_number_input.text().strip()
                employee.full_name = self.full_name_input.text().strip()
                employee.birth_date = self.birth_date_input.date().toPython()
                employee.nationality = self.nationality_input.text().strip() or None
                employee.phone = self.phone_input.text().strip() or None
                employee.address = self.address_input.toPlainText().strip() or None
                employee.hire_date = self.hire_date_input.date().toPython()
                employee.basic_salary = clean_numeric_input(self.basic_salary_input.text())
                employee.department_id = self.department_combo.currentData()
                employee.job_title_id = self.job_title_combo.currentData()
                employee.employment_status = self.employment_status_combo.currentData()

                session.commit()

                # إرسال إشارة النجاح
                self.employee_saved.emit(employee.id)

                success_message = "✅ تم تحديث بيانات الموظف بنجاح!" if self.is_edit_mode else "✅ تم إضافة الموظف بنجاح!"
                show_message(self, "🎉 عملية ناجحة", success_message, "information")

                if self.is_edit_mode:
                    self.accept()
                else:
                    # مسح النموذج للإدخال التالي
                    self.clear_form()
                    self.full_name_input.setFocus()

        except Exception as e:
            show_message(self, "خطأ", f"فشل في حفظ بيانات الموظف: {e}", "error")

    def load_employee_data(self):
        """تحميل بيانات الموظف للتعديل"""
        try:
            with get_db_session_context() as session:
                self.employee = session.query(Employee).filter_by(id=self.employee_id).first()

                if not self.employee:
                    show_message(self, "خطأ", "الموظف غير موجود", "error")
                    self.reject()
                    return

                # ملء الحقول
                self.employee_number_input.setText(self.employee.employee_number)
                self.full_name_input.setText(self.employee.full_name)

                if self.employee.birth_date:
                    self.birth_date_input.setDate(QDate.fromString(str(self.employee.birth_date), "yyyy-MM-dd"))

                if self.employee.nationality:
                    self.nationality_input.setText(self.employee.nationality)

                if self.employee.phone:
                    self.phone_input.setText(self.employee.phone)

                if self.employee.address:
                    self.address_input.setPlainText(self.employee.address)

                self.hire_date_input.setDate(QDate.fromString(str(self.employee.hire_date), "yyyy-MM-dd"))

                # عرض الراتب بالفواصل
                formatted_salary = format_currency(self.employee.basic_salary, show_currency=False)
                self.basic_salary_input.setText(formatted_salary)

                # تعيين القيم المرجعية
                # البحث عن القسم وتعيينه
                for i in range(self.department_combo.count()):
                    if self.department_combo.itemData(i) == self.employee.department_id:
                        self.department_combo.setCurrentIndex(i)
                        break

                # البحث عن العنوان الوظيفي وتعيينه
                for i in range(self.job_title_combo.count()):
                    if self.job_title_combo.itemData(i) == self.employee.job_title_id:
                        self.job_title_combo.setCurrentIndex(i)
                        break

                # البحث عن حالة التوظيف وتعيينها
                for i in range(self.employment_status_combo.count()):
                    if self.employment_status_combo.itemData(i) == self.employee.employment_status:
                        self.employment_status_combo.setCurrentIndex(i)
                        break

        except Exception as e:
            show_message(self, "خطأ", f"فشل في تحميل بيانات الموظف: {e}", "error")
            self.reject()
