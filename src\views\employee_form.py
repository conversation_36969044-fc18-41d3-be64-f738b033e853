"""
نموذج إضافة/تعديل الموظف
Employee Add/Edit Form
"""

from datetime import date
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QGridLayout, QLineEdit, QComboBox,
    QDateEdit, QTextEdit, QPushButton, QLabel, QFrame, QGroupBox, QMessageBox
)
from PySide6.QtCore import Qt, QDate, Signal
from PySide6.QtGui import QFont, QDoubleValidator

from ..utils import (
    apply_rtl_layout, show_message, validate_phone, validate_employee_number,
    clean_numeric_input, generate_employee_number, setup_combobox, get_combobox_value,
    set_combobox_value, validate_employee_data
)
from ..database import get_db_session_context
from ..models import Employee, Department, JobTitle, EmploymentStatus


class EmployeeForm(QDialog):
    """نموذج إضافة/تعديل الموظف"""
    
    # إشارات
    employee_saved = Signal(int)  # إشارة حفظ الموظف
    
    def __init__(self, employee_id: int = None, parent=None):
        super().__init__(parent)
        self.employee_id = employee_id
        self.is_edit_mode = employee_id is not None
        self.employee = None
        
        self.setup_ui()
        self.setup_connections()
        self.load_reference_data()
        
        if self.is_edit_mode:
            self.load_employee_data()
        else:
            self.generate_employee_number()
            
    def setup_ui(self):
        """إعداد واجهة المستخدم المحسنة"""
        self.setObjectName("employee_form")
        apply_rtl_layout(self)

        # إعداد النافذة
        title = "✏️ تعديل الموظف" if self.is_edit_mode else "➕ إضافة موظف جديد"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(1400, 900)  # حجم أكبر ومناسب للحقول
        self.setMinimumSize(1350, 850)  # حد أدنى أكبر لضمان عرض جيد

        # تطبيق الأنماط المحسنة
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            }
        """)

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # إنشاء رأس النموذج المحسن
        self.create_enhanced_header(main_layout)

        # إنشاء تخطيط أفقي للمجموعات
        groups_layout = QHBoxLayout()
        groups_layout.setSpacing(20)  # مساحة بين المجموعات

        # إنشاء مجموعات الحقول المحسنة
        self.create_enhanced_personal_info_group(groups_layout)
        self.create_employee_number_group(groups_layout)

        main_layout.addLayout(groups_layout)

        # إنشاء مجموعة المعلومات الوظيفية
        self.create_enhanced_job_info_group(main_layout)

        # إنشاء أزرار الإجراءات المحسنة
        self.create_enhanced_action_buttons(main_layout)
        
    def create_enhanced_header(self, layout: QVBoxLayout):
        """إنشاء رأس النموذج المحسن"""
        # إطار العنوان الرئيسي
        header_frame = QFrame()
        header_frame.setObjectName("enhanced_header_frame")
        header_frame.setFixedHeight(120)
        header_frame.setStyleSheet("""
            QFrame#enhanced_header_frame {
                background-color: #e3f2fd;
                border: 2px solid #2196f3;
                border-radius: 10px;
                margin: 5px;
            }
        """)

        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(20, 15, 20, 15)
        header_layout.setSpacing(8)

        # العنوان الرئيسي
        title = "✏️ تعديل بيانات الموظف" if self.is_edit_mode else "➕ إضافة موظف جديد"
        self.title_label = QLabel(title)
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setFixedHeight(40)
        self.title_label.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 22px;
                font-weight: bold;
                color: #1976d2;
                background-color: transparent;
                padding: 8px;
                margin: 0px;
            }
        """)

        # العنوان الفرعي
        subtitle = "📝 يرجى ملء جميع الحقول المطلوبة بدقة" if not self.is_edit_mode else "📝 تحديث بيانات الموظف"
        subtitle_label = QLabel(subtitle)
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setFixedHeight(30)
        subtitle_label.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                font-weight: 600;
                color: #424242;
                background-color: #f3e5f5;
                padding: 6px 15px;
                border-radius: 6px;
                border-left: 3px solid #9c27b0;
                margin: 2px;
            }
        """)

        # تجميع العناصر
        header_layout.addWidget(self.title_label)
        header_layout.addWidget(subtitle_label)

        layout.addWidget(header_frame)
        
    def create_enhanced_personal_info_group(self, layout: QHBoxLayout):
        """إنشاء مجموعة المعلومات الشخصية المحسنة"""
        group = QGroupBox("👤 المعلومات الشخصية")
        group.setStyleSheet("""
            QGroupBox {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #3498db;
                border-radius: 12px;
                margin-top: 10px;
                padding-top: 20px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 8px 15px;
                background-color: #3498db;
                color: white;
                border-radius: 8px;
                font-size: 15px;
            }
        """)

        # استخدام تخطيط شبكي للحصول على تنظيم أفضل
        group_layout = QGridLayout(group)
        group_layout.setSpacing(20)
        group_layout.setContentsMargins(25, 30, 25, 25)
        group_layout.setColumnStretch(1, 1)  # العمود الثاني يتمدد
        group_layout.setColumnStretch(3, 1)  # العمود الرابع يتمدد

        # أنماط الحقول المحسنة
        input_style = """
            QLineEdit, QDateEdit, QTextEdit {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 16px;
                padding: 12px 15px;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                background-color: #ffffff;
                min-height: 25px;
                min-width: 300px;
            }
            QLineEdit:focus, QDateEdit:focus, QTextEdit:focus {
                border-color: #3498db;
                background-color: #f8f9ff;
                outline: none;
            }
            QLineEdit:hover, QDateEdit:hover, QTextEdit:hover {
                border-color: #74b9ff;
                background-color: #fdfdff;
            }
        """

        label_style = """
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 16px;
                font-weight: 600;
                color: #2d3436;
                padding: 8px 5px;
                min-width: 140px;
            }
        """

        # الصف الأول: الاسم الكامل (يمتد عبر العرض الكامل)
        name_label = QLabel("👤 الاسم الكامل *:")
        name_label.setStyleSheet(label_style)
        self.full_name_input = QLineEdit()
        self.full_name_input.setMaxLength(200)
        self.full_name_input.setStyleSheet(input_style)
        self.full_name_input.setPlaceholderText("الاسم الثلاثي أو الرباعي")

        group_layout.addWidget(name_label, 0, 0)
        group_layout.addWidget(self.full_name_input, 0, 1, 1, 3)  # يمتد عبر 3 أعمدة

        # الصف الثاني: تاريخ الميلاد والجنسية
        birth_label = QLabel("📅 تاريخ الميلاد:")
        birth_label.setStyleSheet(label_style)
        self.birth_date_input = QDateEdit()
        self.birth_date_input.setDate(QDate.currentDate().addYears(-25))
        self.birth_date_input.setMaximumDate(QDate.currentDate().addYears(-18))
        self.birth_date_input.setCalendarPopup(True)
        self.birth_date_input.setStyleSheet(input_style)

        nationality_label = QLabel("🌍 الجنسية:")
        nationality_label.setStyleSheet(label_style)
        self.nationality_input = QLineEdit()
        self.nationality_input.setMaxLength(50)
        self.nationality_input.setText("عراقي")
        self.nationality_input.setStyleSheet(input_style)

        group_layout.addWidget(birth_label, 1, 0)
        group_layout.addWidget(self.birth_date_input, 1, 1)
        group_layout.addWidget(nationality_label, 1, 2)
        group_layout.addWidget(self.nationality_input, 1, 3)

        # الصف الثالث: رقم الهاتف
        phone_label = QLabel("📞 رقم الهاتف:")
        phone_label.setStyleSheet(label_style)
        self.phone_input = QLineEdit()
        self.phone_input.setMaxLength(20)
        self.phone_input.setPlaceholderText("07901234567")
        self.phone_input.setStyleSheet(input_style)

        group_layout.addWidget(phone_label, 2, 0)
        group_layout.addWidget(self.phone_input, 2, 1, 1, 3)  # يمتد عبر 3 أعمدة

        # الصف الرابع: العنوان (يمتد عبر العرض الكامل)
        address_label = QLabel("🏠 العنوان:")
        address_label.setStyleSheet(label_style)
        self.address_input = QTextEdit()
        self.address_input.setMaximumHeight(80)
        self.address_input.setStyleSheet(input_style)
        self.address_input.setPlaceholderText("العنوان التفصيلي")

        group_layout.addWidget(address_label, 3, 0)
        group_layout.addWidget(self.address_input, 3, 1, 1, 3)  # يمتد عبر 3 أعمدة

        layout.addWidget(group)

    def create_employee_number_group(self, layout: QHBoxLayout):
        """إنشاء مجموعة الرقم الوظيفي"""
        group = QGroupBox("🆔 الرقم الوظيفي")
        group.setStyleSheet("""
            QGroupBox {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #e74c3c;
                border-radius: 12px;
                margin-top: 10px;
                padding-top: 20px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 8px 15px;
                background-color: #e74c3c;
                color: white;
                border-radius: 8px;
                font-size: 15px;
            }
        """)

        # تحديد عرض ثابت للمجموعة
        group.setFixedWidth(280)

        group_layout = QVBoxLayout(group)
        group_layout.setSpacing(15)
        group_layout.setContentsMargins(20, 25, 20, 20)

        # إنشاء حقل الرقم الوظيفي المخفي (للاستخدام الداخلي)
        self.employee_number_input = QLineEdit()
        self.employee_number_input.setMaxLength(20)
        self.employee_number_input.setVisible(False)  # مخفي

        # عرض الرقم الوظيفي
        emp_number_label = QLabel("الرقم الوظيفي:")
        emp_number_label.setAlignment(Qt.AlignCenter)
        emp_number_label.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                font-weight: 600;
                color: #2d3436;
                padding: 8px 5px;
            }
        """)

        self.employee_number_display = QLabel()
        self.employee_number_display.setAlignment(Qt.AlignCenter)
        self.employee_number_display.setMinimumHeight(60)
        self.employee_number_display.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 18px;
                font-weight: bold;
                color: #e74c3c;
                background-color: #fdf2f2;
                border: 2px solid #e74c3c;
                border-radius: 8px;
                padding: 15px 10px;
                margin: 5px;
            }
        """)

        # معلومات إضافية
        info_label = QLabel("يتم توليد الرقم تلقائياً")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                color: #6c757d;
                padding: 5px;
            }
        """)

        group_layout.addWidget(emp_number_label)
        group_layout.addWidget(self.employee_number_display)
        group_layout.addWidget(info_label)
        group_layout.addStretch()  # مساحة فارغة في الأسفل

        layout.addWidget(group)
        
    def create_enhanced_job_info_group(self, layout: QVBoxLayout):
        """إنشاء مجموعة المعلومات الوظيفية المحسنة"""
        group = QGroupBox("💼 المعلومات الوظيفية")
        group.setStyleSheet("""
            QGroupBox {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #27ae60;
                border-radius: 12px;
                margin-top: 10px;
                padding-top: 20px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 8px 15px;
                background-color: #27ae60;
                color: white;
                border-radius: 8px;
                font-size: 15px;
            }
        """)

        # استخدام تخطيط شبكي للمعلومات الوظيفية أيضاً
        group_layout = QGridLayout(group)
        group_layout.setSpacing(20)
        group_layout.setContentsMargins(25, 30, 25, 25)
        group_layout.setColumnStretch(1, 1)  # العمود الثاني يتمدد
        group_layout.setColumnStretch(3, 1)  # العمود الرابع يتمدد

        # أنماط الحقول المحسنة
        input_style = """
            QLineEdit, QDateEdit, QComboBox {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 16px;
                padding: 12px 15px;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                background-color: #ffffff;
                min-height: 25px;
                min-width: 300px;
            }
            QLineEdit:focus, QDateEdit:focus, QComboBox:focus {
                border-color: #27ae60;
                background-color: #f0fff4;
                outline: none;
            }
            QLineEdit:hover, QDateEdit:hover, QComboBox:hover {
                border-color: #55a3ff;
                background-color: #fdfdff;
            }
            QComboBox::drop-down {
                border: none;
                width: 35px;
                background-color: transparent;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 6px solid transparent;
                border-right: 6px solid transparent;
                border-top: 6px solid #27ae60;
                margin-right: 12px;
            }
        """

        label_style = """
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 16px;
                font-weight: 600;
                color: #2d3436;
                padding: 8px 5px;
                min-width: 140px;
            }
        """

        # الصف الأول: تاريخ المباشرة والقسم
        hire_label = QLabel("📅 تاريخ المباشرة *:")
        hire_label.setStyleSheet(label_style)
        self.hire_date_input = QDateEdit()
        self.hire_date_input.setDate(QDate.currentDate())
        self.hire_date_input.setMaximumDate(QDate.currentDate())
        self.hire_date_input.setCalendarPopup(True)
        self.hire_date_input.setStyleSheet(input_style)

        dept_label = QLabel("🏢 القسم *:")
        dept_label.setStyleSheet(label_style)
        self.department_combo = QComboBox()
        self.department_combo.setStyleSheet(input_style)

        group_layout.addWidget(hire_label, 0, 0)
        group_layout.addWidget(self.hire_date_input, 0, 1)
        group_layout.addWidget(dept_label, 0, 2)
        group_layout.addWidget(self.department_combo, 0, 3)

        # الصف الثاني: العنوان الوظيفي وحالة التوظيف
        job_label = QLabel("💼 العنوان الوظيفي *:")
        job_label.setStyleSheet(label_style)
        self.job_title_combo = QComboBox()
        self.job_title_combo.setStyleSheet(input_style)

        status_label = QLabel("📊 حالة التوظيف *:")
        status_label.setStyleSheet(label_style)
        self.employment_status_combo = QComboBox()
        self.employment_status_combo.setStyleSheet(input_style)

        group_layout.addWidget(job_label, 1, 0)
        group_layout.addWidget(self.job_title_combo, 1, 1)
        group_layout.addWidget(status_label, 1, 2)
        group_layout.addWidget(self.employment_status_combo, 1, 3)

        # الصف الثالث: الراتب الأساسي (يمتد عبر العرض الكامل)
        salary_label = QLabel("💰 الراتب الأساسي *:")
        salary_label.setStyleSheet(label_style)
        self.basic_salary_input = QLineEdit()
        self.basic_salary_input.setPlaceholderText("1,000,000 (دينار عراقي)")
        self.basic_salary_input.setStyleSheet(input_style)
        # ربط دالة تنسيق الراتب
        self.basic_salary_input.textChanged.connect(self.format_salary_input)

        group_layout.addWidget(salary_label, 2, 0)
        group_layout.addWidget(self.basic_salary_input, 2, 1, 1, 3)  # يمتد عبر 3 أعمدة

        layout.addWidget(group)
        
    def create_enhanced_action_buttons(self, layout: QVBoxLayout):
        """إنشاء أزرار الإجراءات المحسنة"""
        # إطار الأزرار المحسن
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 12px;
                padding: 20px;
                margin: 15px 0px;
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(20)
        buttons_layout.setContentsMargins(15, 10, 15, 10)

        # زر الحفظ المحسن
        save_text = "✏️ تحديث البيانات" if self.is_edit_mode else "💾 حفظ الموظف"
        self.save_btn = QPushButton(save_text)
        self.save_btn.setMinimumWidth(160)
        self.save_btn.setMinimumHeight(50)
        self.save_btn.setStyleSheet("""
            QPushButton {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 16px;
                font-weight: bold;
                color: white;
                background-color: #28a745;
                border: none;
                border-radius: 10px;
                padding: 15px 25px;
            }
            QPushButton:hover {
                background-color: #218838;
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
            }
            QPushButton:pressed {
                background-color: #1e7e34;
                transform: translateY(0px);
                box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
            }
        """)

        # زر المسح (للنموذج الجديد فقط)
        if not self.is_edit_mode:
            self.clear_btn = QPushButton("🗑️ مسح الحقول")
            self.clear_btn.setMinimumWidth(140)
            self.clear_btn.setMinimumHeight(50)
            self.clear_btn.setStyleSheet("""
                QPushButton {
                    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                    font-size: 16px;
                    font-weight: bold;
                    color: white;
                    background-color: #ffc107;
                    border: none;
                    border-radius: 10px;
                    padding: 15px 25px;
                }
                QPushButton:hover {
                    background-color: #e0a800;
                    transform: translateY(-1px);
                    box-shadow: 0 4px 8px rgba(255, 193, 7, 0.3);
                }
                QPushButton:pressed {
                    background-color: #d39e00;
                    transform: translateY(0px);
                    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
                }
            """)
            self.clear_btn.clicked.connect(self.clear_form)

        # زر الإلغاء المحسن
        self.cancel_btn = QPushButton("❌ إلغاء")
        self.cancel_btn.setMinimumWidth(130)
        self.cancel_btn.setMinimumHeight(50)
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 16px;
                font-weight: bold;
                color: white;
                background-color: #dc3545;
                border: none;
                border-radius: 10px;
                padding: 15px 25px;
            }
            QPushButton:hover {
                background-color: #c82333;
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
            }
            QPushButton:pressed {
                background-color: #bd2130;
                transform: translateY(0px);
                box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
            }
        """)

        buttons_layout.addWidget(self.save_btn)
        if not self.is_edit_mode:
            buttons_layout.addWidget(self.clear_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_btn)

        layout.addWidget(buttons_frame)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.save_btn.clicked.connect(self.save_employee)
        self.cancel_btn.clicked.connect(self.reject)

        # ربط تغيير العنوان الوظيفي بتحديث نطاق الراتب
        self.job_title_combo.currentTextChanged.connect(self.update_salary_range)

        # تعيين التركيز على حقل الاسم الكامل عند فتح النافذة
        from PySide6.QtCore import QTimer
        QTimer.singleShot(100, self.full_name_input.setFocus)

    def clear_form(self):
        """مسح جميع حقول النموذج"""
        # مسح الحقول النصية
        self.employee_number_input.clear()
        self.full_name_input.clear()
        self.nationality_input.setText("عراقي")
        self.phone_input.clear()
        self.address_input.clear()
        self.basic_salary_input.clear()

        # إعادة تعيين التواريخ
        self.birth_date_input.setDate(QDate.currentDate().addYears(-25))
        self.hire_date_input.setDate(QDate.currentDate())

        # إعادة تعيين القوائم المنسدلة
        self.department_combo.setCurrentIndex(0)
        self.job_title_combo.setCurrentIndex(0)
        self.employment_status_combo.setCurrentIndex(0)

        # توليد رقم وظيفي جديد
        self.generate_employee_number()

    def format_salary_input(self, text: str):
        """تنسيق حقل الراتب بالفواصل"""
        # منع التكرار اللانهائي
        if hasattr(self, '_formatting_salary'):
            return

        self._formatting_salary = True

        # إزالة جميع الفواصل والمسافات
        clean_text = ''.join(filter(str.isdigit, text))

        if clean_text:
            try:
                # تحويل إلى رقم وإضافة الفواصل
                number = int(clean_text)
                formatted = f"{number:,}"

                # حفظ موضع المؤشر
                cursor_pos = self.basic_salary_input.cursorPosition()

                # تعيين النص المنسق
                self.basic_salary_input.setText(formatted)

                # إعادة تعيين موضع المؤشر
                new_cursor_pos = min(cursor_pos, len(formatted))
                self.basic_salary_input.setCursorPosition(new_cursor_pos)

            except ValueError:
                pass

        self._formatting_salary = False

    def show_dynamic_success_message(self, message: str):
        """عرض رسالة نجاح ديناميكية ومحسنة"""
        # البحث عن النافذة الرئيسية
        main_window = self.parent()
        while main_window and not hasattr(main_window, 'statusBar'):
            main_window = main_window.parent()

        if main_window and hasattr(main_window, 'statusBar'):
            # عرض في شريط الحالة للنافذة الرئيسية
            main_window.statusBar().showMessage(message, 5000)  # 5 ثوان
        else:
            # عرض رسالة مؤقتة في عنوان النافذة
            original_title = self.windowTitle()
            self.setWindowTitle(f"✅ {message}")

            # إعادة العنوان الأصلي بعد 3 ثوان
            from PySide6.QtCore import QTimer
            timer = QTimer(self)
            timer.timeout.connect(lambda: self.setWindowTitle(original_title))
            timer.setSingleShot(True)
            timer.start(3000)

    def show_interactive_error(self, message: str, target_widget):
        """عرض رسالة خطأ تفاعلية مع توجيه المؤشر للحقل المطلوب"""
        # عرض رسالة الخطأ
        show_message(self, "⚠️ خطأ في البيانات", message, "warning")

        # توجيه التركيز للحقل المطلوب
        target_widget.setFocus()

        # تمييز الحقل بلون مختلف مؤقتاً
        original_style = target_widget.styleSheet()
        error_style = original_style.replace(
            "border: 2px solid #dee2e6",
            "border: 3px solid #dc3545"
        ).replace(
            "background-color: #f8f9fa",
            "background-color: #f8d7da"
        )

        target_widget.setStyleSheet(error_style)

        # إعادة التصميم الأصلي بعد 3 ثوان
        from PySide6.QtCore import QTimer
        timer = QTimer(self)
        timer.timeout.connect(lambda: target_widget.setStyleSheet(original_style))
        timer.setSingleShot(True)
        timer.start(3000)

        # إذا كان الحقل نصي، تحديد النص
        if hasattr(target_widget, 'selectAll'):
            target_widget.selectAll()

    def refresh_parent_table(self):
        """تحديث جدول الموظفين في النافذة الرئيسية"""
        # البحث عن النافذة الرئيسية
        main_window = self.parent()
        while main_window and not hasattr(main_window, 'content_area'):
            main_window = main_window.parent()

        if main_window and hasattr(main_window, 'content_area'):
            # البحث عن صفحة الموظفين
            for i in range(main_window.content_area.count()):
                widget = main_window.content_area.widget(i)
                if hasattr(widget, 'update_data'):
                    # تحديث البيانات في صفحة الموظفين
                    widget.update_data()
                    break

    def load_reference_data(self):
        """تحميل البيانات المرجعية"""
        try:
            with get_db_session_context() as session:
                # تحميل الأقسام
                departments = session.query(Department).filter_by(is_active=True).all()
                dept_items = [(dept.name, dept.id) for dept in departments]
                setup_combobox(self.department_combo, dept_items)
                
                # تحميل العناوين الوظيفية
                job_titles = session.query(JobTitle).filter_by(is_active=True).all()
                title_items = [(title.title, title.id) for title in job_titles]
                setup_combobox(self.job_title_combo, title_items)
                
                # تحميل حالات التوظيف
                status_items = [(status.value, status.name) for status in EmploymentStatus]
                setup_combobox(self.employment_status_combo, status_items, EmploymentStatus.ACTIVE.name)
                
        except Exception as e:
            show_message(self, "خطأ", f"فشل في تحميل البيانات المرجعية: {e}", "error")
            
    def generate_employee_number(self):
        """توليد رقم وظيفي جديد"""
        try:
            with get_db_session_context() as session:
                # الحصول على آخر رقم وظيفي
                last_employee = session.query(Employee).order_by(Employee.id.desc()).first()
                last_number = last_employee.employee_number if last_employee else None

                # توليد رقم جديد
                new_number = generate_employee_number(last_number)
                self.employee_number_input.setText(new_number)

                # تحديث العرض في مجموعة الرقم الوظيفي
                if hasattr(self, 'employee_number_display'):
                    self.employee_number_display.setText(new_number)

        except Exception as e:
            show_message(self, "تحذير", f"فشل في توليد الرقم الوظيفي: {e}", "warning")
            
    def load_employee_data(self):
        """تحميل بيانات الموظف للتعديل"""
        try:
            with get_db_session_context() as session:
                self.employee = session.query(Employee).filter_by(id=self.employee_id).first()
                
                if not self.employee:
                    show_message(self, "خطأ", "الموظف غير موجود", "error")
                    self.reject()
                    return
                
                # ملء الحقول
                self.employee_number_input.setText(self.employee.employee_number)
                self.full_name_input.setText(self.employee.full_name)

                # عرض الرقم الوظيفي في مجموعة الرقم الوظيفي
                if hasattr(self, 'employee_number_display'):
                    self.employee_number_display.setText(self.employee.employee_number)
                
                if self.employee.birth_date:
                    self.birth_date_input.setDate(QDate.fromString(str(self.employee.birth_date), "yyyy-MM-dd"))
                
                if self.employee.nationality:
                    self.nationality_input.setText(self.employee.nationality)
                
                if self.employee.phone:
                    self.phone_input.setText(self.employee.phone)
                
                if self.employee.address:
                    self.address_input.setPlainText(self.employee.address)
                
                self.hire_date_input.setDate(QDate.fromString(str(self.employee.hire_date), "yyyy-MM-dd"))
                # عرض الراتب بالفواصل
                formatted_salary = f"{int(self.employee.basic_salary):,}"
                self.basic_salary_input.setText(formatted_salary)
                
                # تعيين القيم المرجعية
                set_combobox_value(self.department_combo, self.employee.department_id)
                set_combobox_value(self.job_title_combo, self.employee.job_title_id)
                set_combobox_value(self.employment_status_combo, self.employee.employment_status.name)
                
        except Exception as e:
            show_message(self, "خطأ", f"فشل في تحميل بيانات الموظف: {e}", "error")
            self.reject()
            
    def update_salary_range(self):
        """تحديث نطاق الراتب حسب العنوان الوظيفي"""
        job_title_id = get_combobox_value(self.job_title_combo)
        
        if job_title_id:
            try:
                with get_db_session_context() as session:
                    job_title = session.query(JobTitle).filter_by(id=job_title_id).first()
                    
                    if job_title and job_title.min_salary and job_title.max_salary:
                        tooltip = f"نطاق الراتب: {job_title.min_salary:,.0f} - {job_title.max_salary:,.0f} د.ع"
                        self.basic_salary_input.setToolTip(tooltip)
                        
            except Exception:
                pass
                
    def validate_form(self) -> tuple[bool, str]:
        """التحقق من صحة النموذج باستخدام النظام المحسن"""
        # جمع البيانات من الحقول الموجودة
        data = {
            'employee_number': self.employee_number_input.text().strip(),
            'full_name': self.full_name_input.text().strip(),
            'department_id': get_combobox_value(self.department_combo),
            'job_title_id': get_combobox_value(self.job_title_combo),
            'basic_salary': clean_numeric_input(self.basic_salary_input.text()),
            'hire_date': self.hire_date_input.date().toPython(),
            'phone': self.phone_input.text().strip() or None,
            'email': None,  # سيتم إضافة حقل البريد الإلكتروني لاحقاً
            'national_id': None,  # سيتم إضافة حقل رقم الهوية لاحقاً
        }

        # التحقق من صحة البيانات
        is_valid, errors = validate_employee_data(data)

        if not is_valid:
            return False, errors[0]  # إرجاع أول خطأ

        return True, ""
        
    def save_employee(self):
        """حفظ بيانات الموظف"""
        # التحقق من الحقول المطلوبة مع التوجيه التفاعلي
        if not self.employee_number_input.text().strip():
            self.show_interactive_error("🆔 الرقم الوظيفي مطلوب", self.employee_number_input)
            return

        if not self.full_name_input.text().strip():
            self.show_interactive_error("👤 الاسم الكامل مطلوب", self.full_name_input)
            return

        if not get_combobox_value(self.department_combo):
            self.show_interactive_error("🏢 يرجى اختيار القسم", self.department_combo)
            return

        if not get_combobox_value(self.job_title_combo):
            self.show_interactive_error("💼 يرجى اختيار العنوان الوظيفي", self.job_title_combo)
            return

        if not self.basic_salary_input.text().strip():
            self.show_interactive_error("💰 الراتب الأساسي مطلوب", self.basic_salary_input)
            return

        # التحقق من صحة الراتب
        try:
            # إزالة الفواصل قبل التحويل
            salary_text = self.basic_salary_input.text().replace(',', '')
            salary_value = clean_numeric_input(salary_text)
            if salary_value <= 0:
                self.show_interactive_error("💰 يرجى إدخال راتب صحيح أكبر من صفر", self.basic_salary_input)
                return
        except (ValueError, TypeError):
            self.show_interactive_error("💰 يرجى إدخال راتب صحيح بالأرقام فقط", self.basic_salary_input)
            return

        try:
            with get_db_session_context() as session:
                # التحقق من عدم تكرار الرقم الوظيفي
                existing_employee = session.query(Employee).filter(
                    Employee.employee_number == self.employee_number_input.text(),
                    Employee.id != (self.employee_id or 0),
                    Employee.is_active == True
                ).first()

                if existing_employee:
                    self.show_interactive_error(
                        f"🆔 الرقم الوظيفي '{self.employee_number_input.text()}' موجود مسبقاً\nيرجى اختيار رقم آخر",
                        self.employee_number_input
                    )
                    return

                # إنشاء أو تحديث الموظف
                if self.is_edit_mode:
                    employee = session.query(Employee).filter_by(id=self.employee_id).first()
                else:
                    employee = Employee()
                    session.add(employee)

                # تعيين البيانات الأساسية
                employee.employee_number = self.employee_number_input.text().strip()
                employee.full_name = self.full_name_input.text().strip()
                employee.hire_date = self.hire_date_input.date().toPython()
                # إزالة الفواصل من الراتب قبل الحفظ
                salary_text = self.basic_salary_input.text().replace(',', '')
                employee.basic_salary = clean_numeric_input(salary_text)
                employee.department_id = get_combobox_value(self.department_combo)
                employee.job_title_id = get_combobox_value(self.job_title_combo)

                # تعيين البيانات الاختيارية
                if self.birth_date_input.date().isValid():
                    employee.birth_date = self.birth_date_input.date().toPython()

                if self.nationality_input.text().strip():
                    employee.nationality = self.nationality_input.text().strip()

                if self.phone_input.text().strip():
                    employee.phone = self.phone_input.text().strip()

                if self.address_input.toPlainText().strip():
                    employee.address = self.address_input.toPlainText().strip()

                # تعيين حالة التوظيف
                status_name = get_combobox_value(self.employment_status_combo)
                if status_name:
                    employee.employment_status = EmploymentStatus[status_name]
                else:
                    employee.employment_status = EmploymentStatus.ACTIVE

                session.commit()

                # إرسال إشارة النجاح
                self.employee_saved.emit(employee.id)

                # رسالة نجاح ديناميكية ومحسنة
                employee_name = self.full_name_input.text().strip()
                if self.is_edit_mode:
                    success_message = f"✅ تم تحديث بيانات الموظف {employee_name} بنجاح"
                else:
                    success_message = f"✅ تم إضافة الموظف {employee_name} بنجاح"

                # عرض رسالة مؤقتة ديناميكية
                self.show_dynamic_success_message(success_message)

                # تحديث الجدول في النافذة الرئيسية
                self.refresh_parent_table()

                self.accept()

        except Exception as e:
            show_message(self, "خطأ", f"فشل في حفظ بيانات الموظف: {e}", "error")
            print(f"تفاصيل الخطأ: {e}")  # للتشخيص
