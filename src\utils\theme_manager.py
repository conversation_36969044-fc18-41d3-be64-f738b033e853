"""
مدير الأنماط والثيمات
Theme Manager
"""

import os
from pathlib import Path
from typing import Dict, Optional
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QObject, Signal, QSettings


class ThemeManager(QObject):
    """مدير الأنماط والثيمات"""
    
    # إشارات
    theme_changed = Signal(str)  # إشارة تغيير الثيم
    
    def __init__(self):
        super().__init__()
        self.current_theme = "light"
        self.themes = {
            "light": "main.qss",
            "dark": "dark_clean.qss"
        }
        self.styles_dir = Path(__file__).parent.parent / "styles"
        self.settings = QSettings("HR_System", "Theme")
        
        # تحميل الثيم المحفوظ
        self.load_saved_theme()
        
    def get_available_themes(self) -> Dict[str, str]:
        """الحصول على الثيمات المتاحة"""
        return {
            "light": "الوضع النهاري",
            "dark": "الوضع الليلي"
        }
        
    def get_current_theme(self) -> str:
        """الحصول على الثيم الحالي"""
        return self.current_theme
        
    def set_theme(self, theme_name: str) -> bool:
        """تعيين الثيم"""
        if theme_name not in self.themes:
            return False

        try:
            # تحميل ملف الستايل
            style_file = self.styles_dir / self.themes[theme_name]

            if not style_file.exists():
                print(f"ملف الستايل غير موجود: {style_file}")
                return False

            with open(style_file, 'r', encoding='utf-8') as f:
                stylesheet = f.read()

            # تطبيق الستايل على التطبيق
            app = QApplication.instance()
            if app:
                # مسح الأنماط السابقة أولاً
                app.setStyleSheet("")

                # تطبيق الثيم الجديد
                app.setStyleSheet(stylesheet)

                # تحديث إعدادات appearance_manager إذا كان موجوداً
                try:
                    from .appearance_manager import get_appearance_manager
                    appearance_manager = get_appearance_manager()
                    # تحديث الثيم في appearance_manager
                    if hasattr(appearance_manager, 'current_settings'):
                        appearance_manager.current_settings['theme_mode'] = theme_name
                except ImportError:
                    pass  # appearance_manager غير متوفر

            # حفظ الثيم الحالي
            self.current_theme = theme_name
            self.save_theme()

            # إرسال إشارة تغيير الثيم
            self.theme_changed.emit(theme_name)

            print(f"✅ تم تطبيق الثيم: {theme_name}")
            return True

        except Exception as e:
            print(f"❌ خطأ في تحميل الثيم: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    def toggle_theme(self) -> str:
        """تبديل الثيم بين النهاري والليلي"""
        new_theme = "dark" if self.current_theme == "light" else "light"
        self.set_theme(new_theme)
        return new_theme
        
    def load_saved_theme(self):
        """تحميل الثيم المحفوظ"""
        saved_theme = self.settings.value("current_theme", "light")
        if saved_theme in self.themes:
            self.current_theme = saved_theme
        else:
            self.current_theme = "light"
            
    def save_theme(self):
        """حفظ الثيم الحالي"""
        self.settings.setValue("current_theme", self.current_theme)
        
    def apply_current_theme(self) -> bool:
        """تطبيق الثيم الحالي"""
        return self.set_theme(self.current_theme)

    def force_reload_theme(self) -> bool:
        """إعادة تحميل الثيم بقوة (مفيد عند التضارب مع مديري الأنماط الأخرى)"""
        current = self.current_theme
        # تطبيق ثيم مؤقت ثم العودة للثيم الأصلي
        temp_theme = "light" if current == "dark" else "dark"
        self.set_theme(temp_theme)
        return self.set_theme(current)

    def reset_to_default(self):
        """إعادة تعيين الثيم للافتراضي"""
        self.set_theme("light")

    def is_dark_mode(self) -> bool:
        """التحقق من كون الوضع الحالي ليلي"""
        return self.current_theme == "dark"
        
    def get_theme_icon(self, theme_name: str) -> str:
        """الحصول على أيقونة الثيم"""
        icons = {
            "light": "☀️",
            "dark": "🌙"
        }
        return icons.get(theme_name, "🎨")
        
    def create_custom_theme(self, theme_name: str, base_theme: str = "light") -> bool:
        """إنشاء ثيم مخصص"""
        try:
            # نسخ الثيم الأساسي
            base_file = self.styles_dir / self.themes[base_theme]
            custom_file = self.styles_dir / f"{theme_name}.qss"
            
            if base_file.exists():
                with open(base_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                with open(custom_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                    
                # إضافة الثيم الجديد
                self.themes[theme_name] = f"{theme_name}.qss"
                
                return True
                
        except Exception as e:
            print(f"خطأ في إنشاء الثيم المخصص: {e}")
            
        return False
        
    def delete_custom_theme(self, theme_name: str) -> bool:
        """حذف ثيم مخصص"""
        if theme_name in ["light", "dark"]:
            return False  # لا يمكن حذف الثيمات الأساسية
            
        try:
            if theme_name in self.themes:
                theme_file = self.styles_dir / self.themes[theme_name]
                if theme_file.exists():
                    theme_file.unlink()
                    
                del self.themes[theme_name]
                
                # إذا كان الثيم المحذوف هو الحالي، التبديل للثيم الافتراضي
                if self.current_theme == theme_name:
                    self.set_theme("light")
                    
                return True
                
        except Exception as e:
            print(f"خطأ في حذف الثيم: {e}")
            
        return False
        
    def export_theme(self, theme_name: str, export_path: str) -> bool:
        """تصدير ثيم"""
        try:
            if theme_name not in self.themes:
                return False
                
            theme_file = self.styles_dir / self.themes[theme_name]
            export_file = Path(export_path)
            
            if theme_file.exists():
                with open(theme_file, 'r', encoding='utf-8') as src:
                    content = src.read()
                    
                with open(export_file, 'w', encoding='utf-8') as dst:
                    dst.write(content)
                    
                return True
                
        except Exception as e:
            print(f"خطأ في تصدير الثيم: {e}")
            
        return False
        
    def import_theme(self, theme_file_path: str, theme_name: str) -> bool:
        """استيراد ثيم"""
        try:
            source_file = Path(theme_file_path)
            target_file = self.styles_dir / f"{theme_name}.qss"
            
            if source_file.exists():
                with open(source_file, 'r', encoding='utf-8') as src:
                    content = src.read()
                    
                with open(target_file, 'w', encoding='utf-8') as dst:
                    dst.write(content)
                    
                # إضافة الثيم المستورد
                self.themes[theme_name] = f"{theme_name}.qss"
                
                return True
                
        except Exception as e:
            print(f"خطأ في استيراد الثيم: {e}")
            
        return False
        
    def get_theme_preview(self, theme_name: str) -> Optional[str]:
        """الحصول على معاينة الثيم"""
        if theme_name not in self.themes:
            return None
            
        try:
            theme_file = self.styles_dir / self.themes[theme_name]
            
            if theme_file.exists():
                with open(theme_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # استخراج الألوان الأساسية من الملف
                colors = self._extract_colors_from_stylesheet(content)
                return colors
                
        except Exception as e:
            print(f"خطأ في معاينة الثيم: {e}")
            
        return None
        
    def _extract_colors_from_stylesheet(self, stylesheet: str) -> str:
        """استخراج الألوان من ملف الستايل"""
        import re
        
        # البحث عن الألوان في الملف
        color_pattern = r'(background-color|color|border-color):\s*(#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}|rgb\([^)]+\)|rgba\([^)]+\))'
        colors = re.findall(color_pattern, stylesheet)
        
        # تجميع الألوان الفريدة
        unique_colors = set()
        for _, color in colors:
            unique_colors.add(color)
            
        return ", ".join(list(unique_colors)[:10])  # أول 10 ألوان
        
    def reset_to_default(self):
        """إعادة تعيين للثيم الافتراضي"""
        self.set_theme("light")
        
    def get_theme_info(self, theme_name: str) -> Dict[str, str]:
        """الحصول على معلومات الثيم"""
        if theme_name not in self.themes:
            return {}
            
        theme_file = self.styles_dir / self.themes[theme_name]
        
        info = {
            "name": theme_name,
            "display_name": self.get_available_themes().get(theme_name, theme_name),
            "file": self.themes[theme_name],
            "path": str(theme_file),
            "exists": theme_file.exists(),
            "size": theme_file.stat().st_size if theme_file.exists() else 0,
            "icon": self.get_theme_icon(theme_name)
        }
        
        return info


# إنشاء مثيل مدير الأنماط
theme_manager = ThemeManager()
