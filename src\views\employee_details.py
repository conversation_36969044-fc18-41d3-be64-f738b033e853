"""
نافذة تفاصيل الموظف
Employee Details Dialog
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QFrame, QPushButton,
    QTabWidget, QWidget, QTableWidget, QTableWidgetItem, QScrollArea,
    QGridLayout, QGroupBox
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

from ..utils import (
    apply_rtl_layout, setup_table_widget, populate_table,
    format_currency, format_date, get_employment_status_display
)
from ..database import get_db_session_context
from ..models import Employee, FinancialTransaction, SalaryRecord


class EmployeeDetailsDialog(QDialog):
    """نافذة تفاصيل الموظف"""

    def __init__(self, employee_id: int, parent=None):
        super().__init__(parent)
        self.employee_id = employee_id
        self.employee = None

        try:
            self.setup_ui()
            self.load_employee_data()
        except Exception as e:
            print(f"❌ خطأ في إنشاء نافذة التفاصيل: {e}")
            import traceback
            traceback.print_exc()

    def setup_ui(self):
        """إعداد واجهة المستخدم المحسنة"""
        self.setWindowTitle("👤 تفاصيل الموظف")
        self.setModal(True)

        # الحصول على حجم الشاشة
        from PySide6.QtGui import QGuiApplication
        screen = QGuiApplication.primaryScreen()
        screen_geometry = screen.availableGeometry()

        # تعيين العرض الثابت والارتفاع الكامل للشاشة
        window_width = 1400  # عرض أكبر لعرض التفاصيل
        window_height = screen_geometry.height()  # الارتفاع الكامل للشاشة

        self.resize(window_width, window_height)
        self.setMinimumSize(1300, 700)  # حد أدنى معقول

        # توسيط النافذة أفقياً
        x_position = (screen_geometry.width() - window_width) // 2
        y_position = screen_geometry.y()  # بداية الشاشة المتاحة
        self.move(x_position, y_position)

        # تطبيق التخطيط من اليمين إلى اليسار
        apply_rtl_layout(self)

        # تطبيق الأنماط المحسنة
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            }
        """)

        # إنشاء التخطيط الرئيسي مع استغلال المساحة الكاملة
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(25, 25, 25, 25)
        main_layout.setSpacing(20)

        # إنشاء رأس النافذة المحسن
        self.create_enhanced_header(main_layout)

        # إنشاء التبويبات المحسنة
        self.create_enhanced_tabs(main_layout)

        # إنشاء أزرار التحكم المحسنة
        self.create_enhanced_control_buttons(main_layout)

    def create_enhanced_header(self, layout: QVBoxLayout):
        """إنشاء رأس النافذة المحسن"""
        # إطار العنوان الرئيسي المحسن
        header_frame = QFrame()
        header_frame.setObjectName("enhanced_header_frame")
        header_frame.setFixedHeight(160)
        header_frame.setStyleSheet("""
            QFrame#enhanced_header_frame {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                            stop: 0 #667eea, stop: 1 #764ba2);
                border: 3px solid #5a67d8;
                border-radius: 15px;
                margin: 8px;
            }
        """)

        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(20, 15, 20, 15)
        header_layout.setSpacing(10)

        # العنوان الرئيسي المحسن
        self.title_label = QLabel("👤 تفاصيل الموظف")
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setFixedHeight(55)
        self.title_label.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 26px;
                font-weight: bold;
                color: white;
                background-color: transparent;
                padding: 10px;
                margin: 0px;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }
        """)

        # شريط المعلومات السريعة المحسن
        self.quick_info_frame = QFrame()
        self.quick_info_frame.setFixedHeight(60)
        self.quick_info_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 0.9);
                border-radius: 12px;
                border: 2px solid rgba(255, 255, 255, 0.8);
                padding: 8px;
                margin: 5px;
            }
        """)

        self.quick_info_layout = QHBoxLayout(self.quick_info_frame)
        self.quick_info_layout.setContentsMargins(20, 12, 20, 12)
        self.quick_info_layout.setSpacing(30)

        # تجميع العناصر
        header_layout.addWidget(self.title_label)
        header_layout.addWidget(self.quick_info_frame)

        layout.addWidget(header_frame)

    def create_enhanced_tabs(self, layout: QVBoxLayout):
        """إنشاء التبويبات المحسنة"""
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 3px solid #e9ecef;
                border-radius: 12px;
                background-color: white;
                margin-top: 8px;
            }

            QTabBar::tab {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #f8f9fa, stop: 1 #e9ecef);
                border: 2px solid #dee2e6;
                border-bottom: none;
                border-radius: 12px 12px 0px 0px;
                padding: 18px 30px;
                margin-right: 3px;
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 16px;
                font-weight: 600;
                color: #495057;
                min-width: 180px;
                min-height: 20px;
            }

            QTabBar::tab:selected {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #667eea, stop: 1 #764ba2);
                color: white;
                border-color: #5a67d8;
                font-weight: bold;
            }

            QTabBar::tab:hover:!selected {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #e9ecef, stop: 1 #dee2e6);
                color: #2c3e50;
                border-color: #adb5bd;
            }
        """)

        # تبويب المعلومات الشخصية
        self.personal_tab = self.create_enhanced_personal_info_tab()
        self.tab_widget.addTab(self.personal_tab, "👤 المعلومات الشخصية")

        # تبويب المعاملات المالية
        self.financial_tab = self.create_enhanced_financial_tab()
        self.tab_widget.addTab(self.financial_tab, "💰 المعاملات المالية")

        # تبويب الرواتب
        self.salary_tab = self.create_enhanced_salary_tab()
        self.tab_widget.addTab(self.salary_tab, "📊 سجل الرواتب")

        layout.addWidget(self.tab_widget)

    def create_enhanced_personal_info_tab(self):
        """إنشاء تبويب المعلومات الشخصية المحسن"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setSpacing(25)

        # إنشاء منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #f8f9fa;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #6c757d;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #495057;
            }
        """)

        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        scroll_layout.setSpacing(20)

        # مجموعة المعلومات الأساسية المحسنة
        basic_group = QGroupBox("📋 المعلومات الأساسية")
        basic_group.setStyleSheet("""
            QGroupBox {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                border: 3px solid #3498db;
                border-radius: 15px;
                margin-top: 15px;
                padding-top: 20px;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #ffffff, stop: 1 #f8f9fa);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 8px 15px;
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                            stop: 0 #3498db, stop: 1 #2980b9);
                color: white;
                border-radius: 8px;
                font-size: 16px;
            }
        """)
        self.basic_info_layout = QGridLayout(basic_group)
        self.basic_info_layout.setSpacing(20)
        self.basic_info_layout.setContentsMargins(25, 30, 25, 25)
        scroll_layout.addWidget(basic_group)

        # مجموعة معلومات العمل المحسنة
        work_group = QGroupBox("💼 معلومات العمل")
        work_group.setStyleSheet("""
            QGroupBox {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                border: 3px solid #27ae60;
                border-radius: 15px;
                margin-top: 15px;
                padding-top: 20px;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #ffffff, stop: 1 #f8f9fa);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 8px 15px;
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                            stop: 0 #27ae60, stop: 1 #229954);
                color: white;
                border-radius: 8px;
                font-size: 16px;
            }
        """)
        self.work_info_layout = QGridLayout(work_group)
        self.work_info_layout.setSpacing(20)
        self.work_info_layout.setContentsMargins(25, 30, 25, 25)
        scroll_layout.addWidget(work_group)

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)

        return tab

    def create_enhanced_financial_tab(self):
        """إنشاء تبويب المعاملات المالية المحسن"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # عنوان القسم
        title_label = QLabel("💰 المعاملات المالية")
        title_label.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #fff3cd;
                border-left: 4px solid #ffc107;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # جدول المعاملات المالية المحسن
        self.financial_table = QTableWidget()
        headers = ["📅 التاريخ", "📋 النوع", "💵 المبلغ", "✅ المدفوع", "📊 الحالة", "📝 الوصف"]
        column_widths = [120, 140, 120, 120, 120, 250]

        setup_table_widget(
            self.financial_table,
            headers,
            column_widths,
            sortable=True,
            selection_behavior="row"
        )

        # تحسين مظهر الجدول
        self.financial_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 13px;
                gridline-color: #e9ecef;
                selection-background-color: #fff3cd;
                selection-color: #856404;
            }
            QTableWidget::item {
                padding: 10px;
                border-bottom: 1px solid #e9ecef;
                text-align: center;
            }
            QTableWidget::item:selected {
                background-color: #fff3cd;
                color: #856404;
                font-weight: bold;
            }
            QHeaderView::section {
                background-color: #ffc107;
                color: #212529;
                font-weight: bold;
                font-size: 14px;
                padding: 12px;
                border: none;
                border-right: 1px solid #e0a800;
                text-align: center;
            }
        """)

        self.financial_table.setMinimumHeight(500)
        self.financial_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOn)

        layout.addWidget(self.financial_table)

        return tab

    def create_enhanced_salary_tab(self):
        """إنشاء تبويب الرواتب المحسن"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # عنوان القسم
        title_label = QLabel("📊 سجل الرواتب")
        title_label.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #d1ecf1;
                border-left: 4px solid #17a2b8;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # جدول الرواتب المحسن
        self.salary_table = QTableWidget()
        headers = ["📅 الشهر", "📆 السنة", "💰 الراتب الأساسي", "⏰ أيام العمل", "💵 الراتب الصافي", "📊 الحالة"]
        column_widths = [100, 100, 150, 130, 150, 120]

        setup_table_widget(
            self.salary_table,
            headers,
            column_widths,
            sortable=True,
            selection_behavior="row"
        )

        # تحسين مظهر الجدول
        self.salary_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 13px;
                gridline-color: #e9ecef;
                selection-background-color: #d1ecf1;
                selection-color: #0c5460;
            }
            QTableWidget::item {
                padding: 10px;
                border-bottom: 1px solid #e9ecef;
                text-align: center;
            }
            QTableWidget::item:selected {
                background-color: #d1ecf1;
                color: #0c5460;
                font-weight: bold;
            }
            QHeaderView::section {
                background-color: #17a2b8;
                color: white;
                font-weight: bold;
                font-size: 14px;
                padding: 12px;
                border: none;
                border-right: 1px solid #138496;
                text-align: center;
            }
        """)

        self.salary_table.setMinimumHeight(500)
        self.salary_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOn)

        layout.addWidget(self.salary_table)

        return tab

    def create_enhanced_control_buttons(self, layout: QVBoxLayout):
        """إنشاء أزرار التحكم المحسنة"""
        # إطار الأزرار
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 10px;
                padding: 15px;
                margin: 10px 0px;
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(15)

        # زر التعديل المحسن
        self.edit_btn = QPushButton("✏️ تعديل الموظف")
        self.edit_btn.setMinimumWidth(150)
        self.edit_btn.setMinimumHeight(45)
        self.edit_btn.setStyleSheet("""
            QPushButton {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 16px;
                font-weight: bold;
                color: white;
                background-color: #ffc107;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
            }
            QPushButton:hover {
                background-color: #e0a800;
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background-color: #d39e00;
                transform: translateY(0px);
            }
        """)

        # زر الطباعة
        self.print_btn = QPushButton("🖨️ طباعة")
        self.print_btn.setMinimumWidth(120)
        self.print_btn.setMinimumHeight(45)
        self.print_btn.setStyleSheet("""
            QPushButton {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 16px;
                font-weight: bold;
                color: white;
                background-color: #17a2b8;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
            }
            QPushButton:hover {
                background-color: #138496;
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background-color: #117a8b;
                transform: translateY(0px);
            }
        """)

        # زر الإغلاق المحسن
        self.close_btn = QPushButton("❌ إغلاق")
        self.close_btn.setMinimumWidth(120)
        self.close_btn.setMinimumHeight(45)
        self.close_btn.setStyleSheet("""
            QPushButton {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 16px;
                font-weight: bold;
                color: white;
                background-color: #dc3545;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
            }
            QPushButton:hover {
                background-color: #c82333;
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background-color: #bd2130;
                transform: translateY(0px);
            }
        """)
        self.close_btn.clicked.connect(self.close)
        self.edit_btn.clicked.connect(self.edit_employee)
        self.print_btn.clicked.connect(self.print_employee_details)

        buttons_layout.addWidget(self.edit_btn)
        buttons_layout.addWidget(self.print_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.close_btn)

        layout.addWidget(buttons_frame)

    def edit_employee(self):
        """فتح نموذج تعديل الموظف"""
        if hasattr(self, 'employee_data') and self.employee_data:
            from .employee_form import EmployeeForm

            # فتح نموذج التعديل
            edit_dialog = EmployeeForm(employee_id=self.employee_data['id'], parent=self)

            # ربط إشارة الحفظ بتحديث البيانات
            edit_dialog.employee_saved.connect(self.refresh_employee_data)

            edit_dialog.exec()

    def print_employee_details(self):
        """طباعة تفاصيل الموظف"""
        # يمكن تطوير هذه الوظيفة لاحقاً لطباعة تقرير مفصل
        from ..utils import show_message
        show_message(self, "معلومات", "🖨️ وظيفة الطباعة قيد التطوير", "information")

    def refresh_employee_data(self, employee_id: int):
        """تحديث بيانات الموظف بعد التعديل"""
        self.load_employee_data()

    def load_employee_data(self):
        """تحميل بيانات الموظف"""
        try:
            with get_db_session_context() as session:
                employee = session.query(Employee).filter_by(id=self.employee_id).first()

                if not employee:
                    self.title_label.setText("الموظف غير موجود")
                    return

                # نسخ البيانات خارج جلسة قاعدة البيانات لتجنب Session
                self.employee_data = {
                    'id': employee.id,
                    'employee_number': employee.employee_number,
                    'full_name': employee.full_name,
                    'birth_date': employee.birth_date,
                    'nationality': employee.nationality,
                    'phone': employee.phone,
                    'address': employee.address,
                    'hire_date': employee.hire_date,
                    'basic_salary': float(employee.basic_salary),
                    'employment_status': employee.employment_status,
                    'department_name': employee.department.name if employee.department else None,
                    'job_title_name': employee.job_title.title if employee.job_title else None,
                    'age': employee.age,
                    'years_of_service': employee.years_of_service,
                    'daily_salary': employee.daily_salary
                }

                # تحديث العنوان
                self.title_label.setText(f"👤 تفاصيل الموظف: {self.employee_data['full_name']}")

                # تحميل المعلومات السريعة في الرأس
                self.load_quick_info()

                # تحميل المعلومات الأساسية
                self.load_enhanced_basic_info()

                # تحميل المعاملات المالية
                self.load_financial_transactions()

                # تحميل سجل الرواتب
                self.load_salary_records()

        except Exception as e:
            print(f"خطأ في تحميل بيانات الموظف: {e}")
            import traceback
            traceback.print_exc()
            self.title_label.setText(f"خطأ في تحميل البيانات: {e}")

    def load_quick_info(self):
        """تحميل المعلومات السريعة في الرأس"""
        if not hasattr(self, 'employee_data'):
            return

        # مسح المحتوى السابق
        for i in reversed(range(self.quick_info_layout.count())):
            self.quick_info_layout.itemAt(i).widget().setParent(None)

        # المعلومات السريعة
        quick_items = [
            ("🆔", self.employee_data['employee_number']),
            ("🏢", self.employee_data['department_name'] or "غير محدد"),
            ("💰", format_currency(self.employee_data['basic_salary'])),
            ("📅", f"{self.employee_data['years_of_service']} سنة خدمة")
        ]

        for icon, text in quick_items:
            item_frame = QFrame()
            item_frame.setStyleSheet("""
                QFrame {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                                stop: 0 #ffffff, stop: 1 #f8f9fa);
                    border-radius: 10px;
                    padding: 8px;
                    border: 2px solid rgba(255, 255, 255, 0.8);
                    margin: 2px;
                }
                QFrame:hover {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                                stop: 0 #f8f9fa, stop: 1 #e9ecef);
                    border-color: rgba(255, 255, 255, 1.0);
                }
            """)

            item_layout = QHBoxLayout(item_frame)
            item_layout.setContentsMargins(12, 8, 12, 8)
            item_layout.setSpacing(8)

            icon_label = QLabel(icon)
            icon_label.setStyleSheet("""
                font-size: 20px;
                color: #667eea;
                font-weight: bold;
            """)

            text_label = QLabel(text)
            text_label.setStyleSheet("""
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                font-weight: 600;
                color: #2c3e50;
            """)

            item_layout.addWidget(icon_label)
            item_layout.addWidget(text_label)

            self.quick_info_layout.addWidget(item_frame)

    def load_enhanced_basic_info(self):
        """تحميل المعلومات الأساسية المحسنة"""
        if not hasattr(self, 'employee_data'):
            return

        # المعلومات الأساسية
        basic_info = [
            ("🆔 الرقم الوظيفي:", self.employee_data['employee_number']),
            ("👤 الاسم الكامل:", self.employee_data['full_name']),
            ("📅 تاريخ الميلاد:", str(self.employee_data['birth_date']) if self.employee_data['birth_date'] else "غير محدد"),
            ("🎂 العمر:", f"{self.employee_data['age']} سنة" if self.employee_data['birth_date'] else "غير محدد"),
            ("🌍 الجنسية:", self.employee_data['nationality'] or "غير محدد"),
            ("📞 رقم الهاتف:", self.employee_data['phone'] or "غير محدد"),
            ("🏠 العنوان:", self.employee_data['address'] or "غير محدد"),
        ]

        for i, (label, value) in enumerate(basic_info):
            label_widget = QLabel(label)
            label_widget.setStyleSheet("""
                QLabel {
                    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                    font-size: 16px;
                    font-weight: bold;
                    color: #2c3e50;
                    padding: 8px;
                    min-width: 150px;
                }
            """)

            value_widget = QLabel(str(value))
            value_widget.setStyleSheet("""
                QLabel {
                    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                    font-size: 15px;
                    color: #495057;
                    padding: 10px 15px;
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                                stop: 0 #ffffff, stop: 1 #f8f9fa);
                    border-radius: 8px;
                    border: 2px solid #e9ecef;
                    min-height: 20px;
                }
            """)

            self.basic_info_layout.addWidget(label_widget, i, 0)
            self.basic_info_layout.addWidget(value_widget, i, 1)

        # معلومات العمل
        work_info = [
            ("📅 تاريخ المباشرة:", format_date(self.employee_data['hire_date'])),
            ("⏰ سنوات الخدمة:", f"{self.employee_data['years_of_service']} سنة"),
            ("💰 الراتب الأساسي:", format_currency(self.employee_data['basic_salary'])),
            ("💵 الراتب اليومي:", format_currency(self.employee_data['daily_salary'])),
            ("📊 حالة التوظيف:", get_employment_status_display(self.employee_data['employment_status'].name)),
            ("🏢 القسم:", self.employee_data['department_name'] or "غير محدد"),
            ("💼 المنصب الوظيفي:", self.employee_data['job_title_name'] or "غير محدد"),
        ]

        for i, (label, value) in enumerate(work_info):
            label_widget = QLabel(label)
            label_widget.setStyleSheet("""
                QLabel {
                    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                    font-size: 16px;
                    font-weight: bold;
                    color: #2c3e50;
                    padding: 8px;
                    min-width: 150px;
                }
            """)

            value_widget = QLabel(str(value))
            value_widget.setStyleSheet("""
                QLabel {
                    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                    font-size: 15px;
                    color: #495057;
                    padding: 10px 15px;
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                                stop: 0 #ffffff, stop: 1 #f8f9fa);
                    border-radius: 8px;
                    border: 2px solid #e9ecef;
                    min-height: 20px;
                }
            """)

            self.work_info_layout.addWidget(label_widget, i, 0)
            self.work_info_layout.addWidget(value_widget, i, 1)

    def load_financial_transactions(self):
        """تحميل المعاملات المالية"""
        try:
            with get_db_session_context() as session:
                transactions = session.query(FinancialTransaction).filter_by(
                    employee_id=self.employee_id,
                    is_active=True
                ).order_by(FinancialTransaction.transaction_date.desc()).all()

                data = []
                for trans in transactions:
                    row = [
                        format_date(trans.transaction_date),
                        trans.transaction_type.value,
                        format_currency(trans.amount),
                        format_currency(trans.paid_amount),
                        trans.payment_status.value,
                        trans.description or ""
                    ]
                    data.append(row)

                populate_table(self.financial_table, data, editable=False)

        except Exception as e:
            print(f"خطأ في تحميل المعاملات المالية: {e}")

    def load_salary_records(self):
        """تحميل سجل الرواتب"""
        try:
            with get_db_session_context() as session:
                salary_records = session.query(SalaryRecord).filter_by(
                    employee_id=self.employee_id,
                    is_active=True
                ).order_by(SalaryRecord.year.desc(), SalaryRecord.month.desc()).all()

                data = []
                for record in salary_records:
                    row = [
                        str(record.month),
                        str(record.year),
                        format_currency(record.basic_salary),
                        str(record.working_days),
                        format_currency(record.net_salary),
                        record.payment_status.value
                    ]
                    data.append(row)

                populate_table(self.salary_table, data, editable=False)

        except Exception as e:
            print(f"خطأ في تحميل سجل الرواتب: {e}")