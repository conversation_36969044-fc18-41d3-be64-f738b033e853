"""
واجهة إدارة الرواتب
Salaries Management View
"""

from datetime import date
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QComboBox, QLabel, QFrame, QHeaderView, QAbstractItemView,
    QSpinBox, QGroupBox, QCheckBox, QProgressBar
)
from PySide6.QtCore import Qt, Signal, QThread
from PySide6.QtGui import QFont

from ..utils import (
    apply_rtl_layout, setup_table_widget, populate_table, show_message,
    show_confirmation, format_currency, get_month_name
)
from ..database import get_db_session_context
from ..models import Employee, SalaryRecord, FinancialTransaction, TransactionType, PaymentStatus


class SalaryCalculationThread(QThread):
    """خيط حساب الرواتب"""
    
    progress_updated = Signal(int)
    calculation_finished = Signal(list)
    error_occurred = Signal(str)
    
    def __init__(self, month: int, year: int):
        super().__init__()
        self.month = month
        self.year = year
        
    def run(self):
        """تشغيل حساب الرواتب"""
        try:
            with get_db_session_context() as session:
                # الحصول على جميع الموظفين النشطين
                employees = session.query(Employee).filter_by(is_active=True).all()
                total_employees = len(employees)
                
                salary_records = []
                
                for i, employee in enumerate(employees):
                    # التحقق من وجود سجل راتب للفترة
                    existing_record = SalaryRecord.exists_for_period(
                        session, employee.id, self.month, self.year
                    )
                    
                    if not existing_record:
                        # حساب المعاملات المالية للشهر
                        total_advances = employee.get_total_advances(self.month, self.year)
                        total_deductions = employee.get_total_deductions(self.month, self.year)
                        total_bonuses = employee.get_total_bonuses(self.month, self.year)
                        total_debts = employee.get_total_debts(self.month, self.year)
                        
                        # إنشاء سجل راتب جديد
                        salary_record = SalaryRecord(
                            employee_id=employee.id,
                            month=self.month,
                            year=self.year,
                            basic_salary=employee.basic_salary,
                            daily_salary=employee.daily_salary,
                            working_days=30,  # افتراضي
                            total_advances=total_advances,
                            total_deductions=total_deductions,
                            total_bonuses=total_bonuses,
                            total_market_debts=total_debts
                        )
                        
                        # حساب الراتب
                        salary_record.calculate_salary()
                        
                        session.add(salary_record)
                        salary_records.append(salary_record)
                    
                    # تحديث التقدم
                    progress = int((i + 1) / total_employees * 100)
                    self.progress_updated.emit(progress)
                
                session.commit()
                self.calculation_finished.emit(salary_records)
                
        except Exception as e:
            self.error_occurred.emit(str(e))


class SalariesView(QWidget):
    """واجهة إدارة الرواتب"""
    
    # إشارات
    salary_record_selected = Signal(int)  # إشارة اختيار سجل راتب
    
    def __init__(self):
        super().__init__()
        self.current_month = date.today().month
        self.current_year = date.today().year
        self.salary_records_data = []
        self.calculation_thread = None
        
        self.setup_ui()
        self.setup_connections()
        self.load_salary_records()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم المحسن"""
        self.setObjectName("salaries_view")
        apply_rtl_layout(self)

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # إنشاء رأس الصفحة المحسن
        self.create_enhanced_header(main_layout)

        # إنشاء شريط التحكم المحسن
        self.create_enhanced_control_bar(main_layout)

        # إنشاء جدول الرواتب المحسن
        self.create_enhanced_salaries_table(main_layout)

        # إنشاء شريط الأزرار المحسن
        self.create_enhanced_action_buttons(main_layout)

    def update_page_title(self, title: str, subtitle: str):
        """تحديث عنوان الصفحة"""
        self.main_title.setText(title)
        self.subtitle.setText(subtitle)
        
    def create_enhanced_header(self, layout: QVBoxLayout):
        """إنشاء رأس الصفحة المحسن"""
        # إنشاء إطار العنوان الرئيسي المدمج
        header_frame = QFrame()
        header_frame.setObjectName("compact_header_frame")
        header_frame.setFixedHeight(120)  # ارتفاع محسن لتوفير مساحة أفضل للعناوين
        header_frame.setStyleSheet("""
            QFrame#compact_header_frame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #e8f5e8, stop:0.5 #4caf50, stop:1 #2e7d32);
                border: 2px solid #2e7d32;
                border-radius: 10px;
                margin: 5px;
            }
        """)

        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(15, 8, 15, 8)  # تقليل الحشو
        header_layout.setSpacing(5)  # تقليل التباعد

        # العنوان الرئيسي المتغير
        self.main_title = QLabel("💰 إدارة الرواتب")
        self.main_title.setAlignment(Qt.AlignCenter)
        self.main_title.setFixedHeight(60)  # ارتفاع محسن للعنوان الرئيسي
        self.main_title.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 22px;
                font-weight: bold;
                color: white;
                background-color: transparent;
                padding: 8px;
                margin: 0px;
            }
        """)

        # العنوان الفرعي المتغير
        self.subtitle = QLabel("💵 حساب ومتابعة رواتب الموظفين وإدارة المدفوعات")
        self.subtitle.setAlignment(Qt.AlignCenter)
        self.subtitle.setFixedHeight(40)  # ارتفاع محسن للعنوان الفرعي
        self.subtitle.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                font-weight: 600;
                color: #1b5e20;
                background-color: rgba(255, 255, 255, 0.9);
                padding: 6px 15px;
                border-radius: 6px;
                border-left: 3px solid #2e7d32;
                margin: 2px;
            }
        """)

        # تجميع العناصر
        header_layout.addWidget(self.main_title)
        header_layout.addWidget(self.subtitle)

        layout.addWidget(header_frame)
        
    def create_enhanced_control_bar(self, layout: QVBoxLayout):
        """إنشاء شريط التحكم المحسن"""
        # إطار التحكم المحسن
        control_frame = QFrame()
        control_frame.setObjectName("control_frame")
        control_frame.setStyleSheet("""
            QFrame#control_frame {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 10px;
                padding: 15px;
                margin: 10px 0px;
            }
        """)

        control_layout = QVBoxLayout(control_frame)
        control_layout.setSpacing(15)

        # الصف الأول: عنوان التحكم مع الإحصائيات
        first_row = QHBoxLayout()

        # عنوان التحكم
        control_title = QLabel("📅 اختيار الفترة والتحكم")
        control_title.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
            }
        """)

        # الإحصائيات المدمجة
        stats_layout = QHBoxLayout()

        # معلومات الفترة الحالية
        self.period_label = QLabel()
        self.period_label.setFixedHeight(30)
        self.period_label.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                font-weight: bold;
                color: #2c3e50;
                background-color: #e8f5e8;
                padding: 6px 10px;
                border-radius: 4px;
                border: 1px solid #4caf50;
                text-align: center;
            }
        """)
        self.update_period_label()

        # عداد الرواتب
        self.salaries_count_label = QLabel("📊 إجمالي: 0")
        self.salaries_count_label.setFixedHeight(30)
        self.salaries_count_label.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                font-weight: 600;
                color: #2e7d32;
                background-color: #e8f5e8;
                padding: 6px 10px;
                border-radius: 4px;
                border: 1px solid #4caf50;
                text-align: center;
            }
        """)

        # عداد المدفوع
        self.paid_count_label = QLabel("✅ مدفوع: 0")
        self.paid_count_label.setFixedHeight(30)
        self.paid_count_label.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                font-weight: 600;
                color: #1b5e20;
                background-color: #c8e6c9;
                padding: 6px 10px;
                border-radius: 4px;
                border: 1px solid #2e7d32;
                text-align: center;
            }
        """)

        # عداد غير المدفوع
        self.unpaid_count_label = QLabel("⏳ معلق: 0")
        self.unpaid_count_label.setFixedHeight(30)
        self.unpaid_count_label.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                font-weight: 600;
                color: #d32f2f;
                background-color: #ffebee;
                padding: 6px 10px;
                border-radius: 4px;
                border: 1px solid #f44336;
                text-align: center;
            }
        """)

        stats_layout.addWidget(self.period_label)
        stats_layout.addWidget(self.salaries_count_label)
        stats_layout.addWidget(self.paid_count_label)
        stats_layout.addWidget(self.unpaid_count_label)
        stats_layout.addStretch()

        first_row.addWidget(control_title)
        first_row.addStretch()
        first_row.addLayout(stats_layout)

        # الصف الثاني: عناصر التحكم
        second_row = QHBoxLayout()

        # اختيار الشهر
        month_label = QLabel("📅 الشهر:")
        month_label.setStyleSheet("""
            QLabel {
                font-size: 13px;
                font-weight: 600;
                color: #495057;
                min-width: 50px;
            }
        """)

        self.month_combo = QComboBox()
        self.month_combo.setMinimumHeight(35)
        self.month_combo.setStyleSheet("""
            QComboBox {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 13px;
                padding: 8px 12px;
                border: 2px solid #4caf50;
                border-radius: 8px;
                background-color: #ffffff;
                min-width: 120px;
            }
            QComboBox:focus {
                border-color: #2e7d32;
                background-color: #f1f8e9;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
                background-color: #4caf50;
                border-radius: 6px;
                margin: 2px;
            }
        """)
        months = [(get_month_name(i), i) for i in range(1, 13)]
        for month_name, month_num in months:
            self.month_combo.addItem(month_name, month_num)
        self.month_combo.setCurrentIndex(self.current_month - 1)

        # اختيار السنة
        year_label = QLabel("📆 السنة:")
        year_label.setStyleSheet("""
            QLabel {
                font-size: 13px;
                font-weight: 600;
                color: #495057;
                min-width: 50px;
            }
        """)

        self.year_spin = QSpinBox()
        self.year_spin.setRange(2020, 2030)
        self.year_spin.setValue(self.current_year)
        self.year_spin.setMinimumHeight(35)
        self.year_spin.setStyleSheet("""
            QSpinBox {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 13px;
                padding: 8px 12px;
                border: 2px solid #4caf50;
                border-radius: 8px;
                background-color: #ffffff;
                min-width: 80px;
            }
            QSpinBox:focus {
                border-color: #2e7d32;
                background-color: #f1f8e9;
            }
        """)

        # زر حساب الرواتب
        self.calculate_btn = QPushButton("🧮 حساب الرواتب")
        self.calculate_btn.setMinimumWidth(140)
        self.calculate_btn.setMinimumHeight(40)
        self.calculate_btn.setStyleSheet("""
            QPushButton {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4caf50, stop:1 #2e7d32);
                border: 2px solid #1b5e20;
                border-radius: 10px;
                padding: 10px 15px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #66bb6a, stop:1 #4caf50);
                border-color: #2e7d32;
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2e7d32, stop:1 #1b5e20);
            }
            QPushButton:disabled {
                background-color: #9e9e9e;
                border-color: #757575;
                color: #bdbdbd;
            }
        """)

        second_row.addWidget(month_label)
        second_row.addWidget(self.month_combo)
        second_row.addWidget(year_label)
        second_row.addWidget(self.year_spin)
        second_row.addWidget(self.calculate_btn)
        second_row.addStretch()

        control_layout.addLayout(first_row)
        control_layout.addLayout(second_row)

        # شريط التقدم المحسن
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMinimumHeight(25)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #4caf50;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                color: #2e7d32;
                background-color: #f1f8e9;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4caf50, stop:1 #2e7d32);
                border-radius: 6px;
                margin: 1px;
            }
        """)

        layout.addWidget(control_frame)
        layout.addWidget(self.progress_bar)
        
    def create_enhanced_salaries_table(self, layout: QVBoxLayout):
        """إنشاء جدول الرواتب المحسن"""
        self.salaries_table = QTableWidget()

        # إعداد الجدول مع أعمدة محسنة
        headers = [
            "الموظف", "الراتب الأساسي", "الراتب اليومي", "أيام الدوام",
            "إجمالي السلف", "إجمالي الخصومات", "إجمالي المكافآت", "ديون الماركت",
            "إجمالي الراتب", "صافي الراتب", "حالة الصرف"
        ]

        # أعمدة أكبر وأكثر وضوحاً مع توزيع أفضل للمساحة
        column_widths = [180, 140, 130, 110, 130, 140, 140, 130, 140, 140, 120]

        setup_table_widget(
            self.salaries_table,
            headers,
            column_widths,
            sortable=True,
            selection_behavior="row"
        )

        # تحسين مظهر الجدول
        self.salaries_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 13px;
                gridline-color: #e9ecef;
                selection-background-color: #e8f5e8;
                selection-color: #2c3e50;
            }

            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e9ecef;
                text-align: center;
                color: #2c3e50;
            }

            QTableWidget::item:selected {
                background-color: #e8f5e8;
                color: #2c3e50;
                font-weight: bold;
            }

            QTableWidget::item:hover {
                background-color: #f1f8e9;
            }

            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4caf50, stop:1 #2e7d32);
                color: white;
                font-weight: bold;
                font-size: 13px;
                padding: 10px;
                border: none;
                border-right: 1px solid #1b5e20;
                text-align: center;
            }

            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #66bb6a, stop:1 #4caf50);
            }
        """)

        # تمكين قائمة السياق
        self.salaries_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.salaries_table.customContextMenuRequested.connect(self.show_context_menu)

        layout.addWidget(self.salaries_table)

    def show_context_menu(self, position):
        """عرض قائمة السياق للجدول"""
        # التحقق من وجود عنصر في الموضع المحدد
        item = self.salaries_table.itemAt(position)
        if not item:
            return

        # إنشاء قائمة السياق
        from PySide6.QtWidgets import QMenu
        context_menu = QMenu(self)

        # الحصول على الصف المحدد
        row = self.salaries_table.currentRow()
        has_selection = 0 <= row < len(self.salary_records_data)

        if has_selection:
            employee_name = self.salary_records_data[row][0]
            net_salary = self.salary_records_data[row][9]
            is_paid = self.salary_records_data[row][10] == "مدفوع"

            # إضافة عنوان القائمة
            title_action = context_menu.addAction(f"💰 {employee_name}")
            title_action.setEnabled(False)

            subtitle_action = context_menu.addAction(f"💵 صافي الراتب: {net_salary}")
            subtitle_action.setEnabled(False)
            context_menu.addSeparator()

            # إضافة الإجراءات
            edit_action = context_menu.addAction("✏️ تعديل تفاصيل الراتب")
            edit_action.triggered.connect(self.edit_salary_details)

            if not is_paid:
                pay_action = context_menu.addAction("💳 صرف الراتب")
                pay_action.triggered.connect(self.pay_selected_salary)
            else:
                paid_action = context_menu.addAction("✅ تم الصرف")
                paid_action.setEnabled(False)

            context_menu.addSeparator()

            details_action = context_menu.addAction("📋 عرض التفاصيل الكاملة")
            details_action.triggered.connect(self.edit_salary_details)

            # عرض القائمة
            context_menu.exec(self.salaries_table.mapToGlobal(position))
        
    def create_enhanced_action_buttons(self, layout: QVBoxLayout):
        """إنشاء شريط الأزرار المحسن"""
        # إطار الأزرار المدمج
        buttons_frame = QFrame()
        buttons_frame.setObjectName("compact_buttons_frame")
        buttons_frame.setStyleSheet("""
            QFrame#compact_buttons_frame {
                background-color: #ffffff;
                border: 1px solid #e9ecef;
                border-radius: 6px;
                padding: 8px;
                margin: 5px 0px;
                max-height: 60px;
            }
        """)

        # تخطيط أفقي مدمج
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(8)
        buttons_layout.setContentsMargins(10, 5, 10, 5)

        # أزرار الصرف المحسنة
        self.edit_salary_btn = QPushButton("✏️ تعديل")
        self.edit_salary_btn.setEnabled(False)
        self.edit_salary_btn.setStyleSheet("""
            QPushButton {
                font-size: 12px;
                font-weight: bold;
                color: white;
                background-color: #ff9800;
                border: none;
                border-radius: 5px;
                padding: 8px 12px;
                min-width: 70px;
                min-height: 35px;
            }
            QPushButton:hover:enabled {
                background-color: #f57c00;
            }
            QPushButton:pressed:enabled {
                background-color: #ef6c00;
            }
            QPushButton:disabled {
                background-color: #6c757d;
                color: #adb5bd;
            }
        """)

        self.pay_selected_btn = QPushButton("💳 صرف محدد")
        self.pay_selected_btn.setEnabled(False)
        self.pay_selected_btn.setStyleSheet("""
            QPushButton {
                font-size: 12px;
                font-weight: bold;
                color: white;
                background-color: #28a745;
                border: none;
                border-radius: 5px;
                padding: 8px 12px;
                min-width: 90px;
                min-height: 35px;
            }
            QPushButton:hover:enabled {
                background-color: #218838;
            }
            QPushButton:pressed:enabled {
                background-color: #1e7e34;
            }
            QPushButton:disabled {
                background-color: #6c757d;
                color: #adb5bd;
            }
        """)

        self.pay_department_btn = QPushButton("🏢 صرف قسم")
        self.pay_department_btn.setEnabled(False)
        self.pay_department_btn.setStyleSheet("""
            QPushButton {
                font-size: 12px;
                font-weight: bold;
                color: white;
                background-color: #17a2b8;
                border: none;
                border-radius: 5px;
                padding: 8px 12px;
                min-width: 80px;
                min-height: 35px;
            }
            QPushButton:hover:enabled {
                background-color: #138496;
            }
            QPushButton:pressed:enabled {
                background-color: #117a8b;
            }
            QPushButton:disabled {
                background-color: #6c757d;
                color: #adb5bd;
            }
        """)

        self.pay_all_btn = QPushButton("💰 صرف الكل")
        self.pay_all_btn.setStyleSheet("""
            QPushButton {
                font-size: 12px;
                font-weight: bold;
                color: white;
                background-color: #4caf50;
                border: none;
                border-radius: 5px;
                padding: 8px 12px;
                min-width: 80px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)

        # أزرار التقارير المحسنة
        self.preview_payroll_btn = QPushButton("📋 معاينة")
        self.preview_payroll_btn.setStyleSheet("""
            QPushButton {
                font-size: 12px;
                font-weight: bold;
                color: white;
                background-color: #6f42c1;
                border: none;
                border-radius: 5px;
                padding: 8px 12px;
                min-width: 70px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
            QPushButton:pressed {
                background-color: #4e2a87;
            }
        """)

        self.export_btn = QPushButton("📤 تصدير")
        self.export_btn.setStyleSheet("""
            QPushButton {
                font-size: 12px;
                font-weight: bold;
                color: white;
                background-color: #007bff;
                border: none;
                border-radius: 5px;
                padding: 8px 12px;
                min-width: 60px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
        """)

        self.refresh_btn = QPushButton("🔄 تحديث")
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                font-size: 12px;
                font-weight: bold;
                color: white;
                background-color: #6c757d;
                border: none;
                border-radius: 5px;
                padding: 8px 12px;
                min-width: 70px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
            QPushButton:pressed {
                background-color: #495057;
            }
        """)

        # ترتيب الأزرار المدمجة
        buttons_layout.addWidget(self.edit_salary_btn)
        buttons_layout.addWidget(self.pay_selected_btn)
        buttons_layout.addWidget(self.pay_department_btn)
        buttons_layout.addWidget(self.pay_all_btn)
        buttons_layout.addWidget(self.preview_payroll_btn)
        buttons_layout.addWidget(self.export_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.refresh_btn)

        layout.addWidget(buttons_frame)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        # ربط إشارات التحكم
        self.month_combo.currentIndexChanged.connect(self.on_period_changed)
        self.year_spin.valueChanged.connect(self.on_period_changed)
        self.calculate_btn.clicked.connect(self.calculate_salaries)
        
        # ربط إشارات الجدول
        self.salaries_table.itemSelectionChanged.connect(self.on_selection_changed)
        
        # ربط إشارات الأزرار
        self.edit_salary_btn.clicked.connect(self.edit_salary_details)
        self.pay_selected_btn.clicked.connect(self.pay_selected_salary)
        self.pay_department_btn.clicked.connect(self.pay_department_salaries)
        self.pay_all_btn.clicked.connect(self.pay_all_salaries)
        self.preview_payroll_btn.clicked.connect(self.preview_payroll)
        self.export_btn.clicked.connect(self.export_salaries)
        self.refresh_btn.clicked.connect(self.load_salary_records)
        
    def update_period_label(self):
        """تحديث تسمية الفترة"""
        month_name = get_month_name(self.current_month)
        self.period_label.setText(f"الفترة: {month_name} {self.current_year}")
        
    def on_period_changed(self):
        """معالج تغيير الفترة"""
        self.current_month = self.month_combo.currentData()
        self.current_year = self.year_spin.value()
        self.update_period_label()
        self.load_salary_records()
        
    def calculate_salaries(self):
        """حساب الرواتب"""
        if show_confirmation(
            self,
            "تأكيد حساب الرواتب",
            f"هل أنت متأكد من حساب رواتب {get_month_name(self.current_month)} {self.current_year}؟\n\nسيتم إنشاء سجلات جديدة للموظفين الذين لا يملكون سجلات للفترة المحددة."
        ):
            # إظهار شريط التقدم
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.calculate_btn.setEnabled(False)
            
            # بدء خيط الحساب
            self.calculation_thread = SalaryCalculationThread(self.current_month, self.current_year)
            self.calculation_thread.progress_updated.connect(self.progress_bar.setValue)
            self.calculation_thread.calculation_finished.connect(self.on_calculation_finished)
            self.calculation_thread.error_occurred.connect(self.on_calculation_error)
            self.calculation_thread.start()
            
    def on_calculation_finished(self, salary_records):
        """معالج انتهاء حساب الرواتب"""
        self.progress_bar.setVisible(False)
        self.calculate_btn.setEnabled(True)
        
        if salary_records:
            show_message(self, "نجح", f"تم حساب {len(salary_records)} راتب بنجاح", "information")
        else:
            show_message(self, "معلومات", "جميع الرواتب محسوبة مسبقاً للفترة المحددة", "information")
        
        self.load_salary_records()
        
    def on_calculation_error(self, error_message):
        """معالج خطأ حساب الرواتب"""
        self.progress_bar.setVisible(False)
        self.calculate_btn.setEnabled(True)
        show_message(self, "خطأ", f"فشل في حساب الرواتب: {error_message}", "error")
        
    def load_salary_records(self):
        """تحميل سجلات الرواتب"""
        try:
            with get_db_session_context() as session:
                salary_records = SalaryRecord.get_monthly_records(
                    session, self.current_month, self.current_year
                )
                
                self.salary_records_data = []
                for record in salary_records:
                    row_data = [
                        record.employee.full_name if record.employee else "",
                        format_currency(record.basic_salary),
                        format_currency(record.daily_salary),
                        str(record.working_days),
                        format_currency(record.total_advances),
                        format_currency(record.total_deductions),
                        format_currency(record.total_bonuses),
                        format_currency(record.total_market_debts),
                        format_currency(record.gross_salary),
                        format_currency(record.net_salary),
                        "مدفوع" if record.is_paid else "غير مدفوع"
                    ]
                    # إضافة ID للاستخدام الداخلي
                    row_data.append(record.id)
                    self.salary_records_data.append(row_data)
                
                # تحديث الجدول
                self.update_table()

                # تحديث الإحصائيات
                total_salaries = len(self.salary_records_data)
                paid_count = len([row for row in self.salary_records_data if row[10] == "مدفوع"])
                unpaid_count = total_salaries - paid_count

                self.salaries_count_label.setText(f"📊 إجمالي: {total_salaries}")
                self.paid_count_label.setText(f"✅ مدفوع: {paid_count}")
                self.unpaid_count_label.setText(f"⏳ معلق: {unpaid_count}")

                # تحديث حالة الأزرار
                has_records = len(self.salary_records_data) > 0
                self.pay_department_btn.setEnabled(has_records)
                self.pay_all_btn.setEnabled(has_records)
                self.preview_payroll_btn.setEnabled(has_records)
                self.export_btn.setEnabled(has_records)
                
        except Exception as e:
            show_message(self, "خطأ", f"فشل في تحميل سجلات الرواتب: {e}", "error")
            
    def update_table(self):
        """تحديث الجدول"""
        # إزالة العمود الأخير (ID) من العرض
        display_data = [row[:-1] for row in self.salary_records_data]
        populate_table(self.salaries_table, display_data, editable=False)
        
    def on_selection_changed(self):
        """معالج تغيير التحديد"""
        has_selection = len(self.salaries_table.selectedItems()) > 0
        self.edit_salary_btn.setEnabled(has_selection)
        self.pay_selected_btn.setEnabled(has_selection)

        if has_selection:
            row = self.salaries_table.currentRow()
            if 0 <= row < len(self.salary_records_data):
                salary_record_id = self.salary_records_data[row][-1]
                self.salary_record_selected.emit(salary_record_id)
                
    def pay_selected_salary(self):
        """صرف راتب محدد"""
        row = self.salaries_table.currentRow()
        if 0 <= row < len(self.salary_records_data):
            employee_name = self.salary_records_data[row][0]
            net_salary = self.salary_records_data[row][9]
            
            if show_confirmation(
                self,
                "تأكيد صرف الراتب",
                f"هل أنت متأكد من صرف راتب '{employee_name}'؟\n\nصافي الراتب: {net_salary}"
            ):
                salary_record_id = self.salary_records_data[row][-1]
                self.pay_salary(salary_record_id)
                
    def pay_department_salaries(self):
        """صرف رواتب قسم"""
        # سيتم تطوير هذه الميزة لاحقاً
        show_message(self, "معلومات", "ميزة صرف رواتب القسم قيد التطوير", "information")
        
    def pay_all_salaries(self):
        """صرف جميع الرواتب"""
        unpaid_count = sum(1 for row in self.salary_records_data if row[10] == "غير مدفوع")
        
        if unpaid_count == 0:
            show_message(self, "معلومات", "جميع الرواتب مدفوعة", "information")
            return
        
        if show_confirmation(
            self,
            "تأكيد صرف جميع الرواتب",
            f"هل أنت متأكد من صرف جميع الرواتب؟\n\nعدد الرواتب غير المدفوعة: {unpaid_count}"
        ):
            try:
                with get_db_session_context() as session:
                    updated_count = 0
                    for row in self.salary_records_data:
                        if row[10] == "غير مدفوع":
                            salary_record_id = row[-1]
                            salary_record = session.query(SalaryRecord).filter_by(id=salary_record_id).first()
                            if salary_record and salary_record.pay_salary():
                                updated_count += 1
                    
                    session.commit()
                    
                    if updated_count > 0:
                        show_message(self, "نجح", f"تم صرف {updated_count} راتب بنجاح", "information")
                        self.load_salary_records()
                    
            except Exception as e:
                show_message(self, "خطأ", f"فشل في صرف الرواتب: {e}", "error")
                
    def pay_salary(self, salary_record_id: int):
        """صرف راتب واحد"""
        try:
            with get_db_session_context() as session:
                salary_record = session.query(SalaryRecord).filter_by(id=salary_record_id).first()
                if salary_record:
                    if salary_record.pay_salary():
                        session.commit()
                        show_message(self, "نجح", "تم صرف الراتب بنجاح", "information")
                        self.load_salary_records()
                    else:
                        show_message(self, "تحذير", "الراتب مدفوع مسبقاً", "warning")
                else:
                    show_message(self, "خطأ", "سجل الراتب غير موجود", "error")
                    
        except Exception as e:
            show_message(self, "خطأ", f"فشل في صرف الراتب: {e}", "error")
            
    def preview_payroll(self):
        """معاينة مسير الرواتب"""
        # سيتم تطوير هذه الميزة لاحقاً
        show_message(self, "معلومات", "ميزة معاينة مسير الرواتب قيد التطوير", "information")
        
    def edit_salary_details(self):
        """تعديل تفاصيل الراتب"""
        row = self.salaries_table.currentRow()
        print(f"الصف المحدد: {row}")

        if 0 <= row < len(self.salary_records_data):
            salary_record_id = self.salary_records_data[row][-1]
            print(f"معرف سجل الراتب: {salary_record_id}")

            # استيراد نافذة التفاصيل
            from .salary_details_dialog import SalaryDetailsDialog

            print("فتح نافذة تعديل تفاصيل الراتب...")
            dialog = SalaryDetailsDialog(salary_record_id, self)
            dialog.salary_updated.connect(self.on_salary_updated)
            result = dialog.exec()
            print(f"نتيجة النافذة: {result}")
        else:
            print("لم يتم تحديد صف صحيح")

    def on_salary_updated(self, salary_record_id: int):
        """معالج تحديث الراتب"""
        # إعادة تحميل البيانات
        self.load_salary_records()
        show_message(self, "نجح", "تم تحديث تفاصيل الراتب بنجاح", "information")

    def export_salaries(self):
        """تصدير الرواتب"""
        # سيتم تطوير هذه الميزة لاحقاً
        show_message(self, "معلومات", "ميزة تصدير الرواتب قيد التطوير", "information")
