"""
واجهة إدارة الموظفين
Employees Management View
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLineEdit, QComboBox, QLabel, QFrame, QHeaderView,
    QAbstractItemView, QMessageBox, QSizePolicy, QGridLayout, QAbstractScrollArea
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QColor

from ..utils import (
    apply_rtl_layout, setup_table_widget, populate_table, show_message,
    show_confirmation, format_currency, format_date, get_employment_status_display,
    create_search_builder
)
from ..database import get_db_session_context
from ..models import Employee, Department, JobTitle, EmploymentStatus


class EmployeesView(QWidget):
    """واجهة إدارة الموظفين"""
    
    # إشارات
    employee_selected = Signal(int)  # إشارة اختيار موظف
    add_employee_requested = Signal()  # إشارة طلب إضافة موظف
    edit_employee_requested = Signal(int)  # إشارة طلب تعديل موظف
    
    def __init__(self):
        super().__init__()
        self.employees_data = []
        self.filtered_data = []
        
        self.setup_ui()
        self.setup_connections()
        self.load_employees()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setObjectName("employees_view")
        apply_rtl_layout(self)

        # إنشاء التخطيط الرئيسي مع استغلال المساحة الكاملة
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(25, 25, 25, 25)
        main_layout.setSpacing(25)

        # إنشاء رأس الصفحة
        self.create_header(main_layout)

        # إنشاء شريط البحث والتصفية
        self.create_search_bar(main_layout)

        # إنشاء جدول الموظفين
        self.create_employees_table(main_layout)

        # إنشاء شريط الأزرار
        self.create_action_buttons(main_layout)
        
    def create_header(self, layout: QVBoxLayout):
        """إنشاء رأس الصفحة المحسن"""
        # إنشاء إطار العنوان الرئيسي المحسن
        header_frame = QFrame()
        header_frame.setObjectName("enhanced_header_frame")
        header_frame.setFixedHeight(140)  # ارتفاع أكبر للمظهر الأفضل
        header_frame.setStyleSheet("""
            QFrame#enhanced_header_frame {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                            stop: 0 #667eea, stop: 1 #764ba2);
                border: 3px solid #5a67d8;
                border-radius: 15px;
                margin: 8px;
            }
        """)

        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(20, 15, 20, 15)  # حشو أكبر للمظهر الأفضل
        header_layout.setSpacing(8)  # تباعد محسن

        # العنوان الرئيسي المحسن
        self.main_title = QLabel("👥 إدارة الموظفين")
        self.main_title.setAlignment(Qt.AlignCenter)
        self.main_title.setFixedHeight(65)  # ارتفاع أكبر للمظهر الأفضل
        self.main_title.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 26px;
                font-weight: bold;
                color: white;
                background-color: transparent;
                padding: 10px;
                margin: 0px;

            }
        """)

        # العنوان الفرعي المحسن
        self.subtitle = QLabel("💼 إدارة شاملة لبيانات الموظفين والمعلومات الوظيفية")
        self.subtitle.setAlignment(Qt.AlignCenter)
        self.subtitle.setFixedHeight(45)  # ارتفاع أكبر للمظهر الأفضل
        self.subtitle.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                font-weight: 600;
                color: rgba(255, 255, 255, 0.9);
                background-color: rgba(255, 255, 255, 0.1);
                padding: 8px 20px;
                border-radius: 10px;
                border: 2px solid rgba(255, 255, 255, 0.3);
                margin: 5px;

            }
        """)

        # تجميع العناصر
        header_layout.addWidget(self.main_title)
        header_layout.addWidget(self.subtitle)

        layout.addWidget(header_frame)

    def update_page_title(self, title: str, subtitle: str):
        """تحديث عنوان الصفحة"""
        self.main_title.setText(title)
        self.subtitle.setText(subtitle)

    def create_search_bar(self, layout: QVBoxLayout):
        """إنشاء شريط البحث والتصفية المحسن"""
        search_frame = QFrame()
        search_frame.setObjectName("search_frame")
        search_frame.setStyleSheet("""
            QFrame#search_frame {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #ffffff, stop: 1 #f8f9fa);
                border: 3px solid #e9ecef;
                border-radius: 15px;
                padding: 20px;
                margin: 15px 0px;
            }
        """)

        search_layout = QVBoxLayout(search_frame)
        search_layout.setSpacing(10)

        # الصف الأول: عنوان البحث مع الإحصائيات
        first_row = QHBoxLayout()

        # عنوان البحث المحسن
        search_title = QLabel("🔍 البحث والتصفية")
        search_title.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }
        """)

        # الإحصائيات المدمجة
        stats_layout = QHBoxLayout()

        # عداد الموظفين المحسن
        self.employees_count_label = QLabel("📊 إجمالي: 0")
        self.employees_count_label.setFixedHeight(35)
        self.employees_count_label.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #ffffff, stop: 1 #ecf0f1);
                padding: 8px 15px;
                border-radius: 8px;
                border: 2px solid #3498db;
                text-align: center;
            }
        """)

        # عداد الموظفين النشطين المحسن
        self.active_employees_label = QLabel("✅ نشط: 0")
        self.active_employees_label.setFixedHeight(35)
        self.active_employees_label.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                font-weight: 600;
                color: #27ae60;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #ffffff, stop: 1 #d5f4e6);
                padding: 8px 15px;
                border-radius: 8px;
                border: 2px solid #27ae60;
                text-align: center;
            }
        """)

        # عداد الأقسام المحسن
        self.departments_count_label = QLabel("🏢 أقسام: 0")
        self.departments_count_label.setFixedHeight(35)
        self.departments_count_label.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                font-weight: 600;
                color: #8e44ad;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #ffffff, stop: 1 #f4ecf7);
                padding: 8px 15px;
                border-radius: 8px;
                border: 2px solid #8e44ad;
                text-align: center;
            }
        """)

        stats_layout.addWidget(self.employees_count_label)
        stats_layout.addWidget(self.active_employees_label)
        stats_layout.addWidget(self.departments_count_label)
        stats_layout.addStretch()

        first_row.addWidget(search_title)
        first_row.addStretch()
        first_row.addLayout(stats_layout)

        # الصف الثاني: حقل البحث والتصفية
        second_row = QHBoxLayout()

        search_label = QLabel("🔎 البحث السريع:")
        search_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                font-weight: 600;
                color: #495057;
                min-width: 100px;
            }
        """)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث بالاسم أو الرقم الوظيفي...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 15px;
                padding: 10px 15px;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #ffffff, stop: 1 #f8f9fa);
                min-height: 20px;
            }
            QLineEdit:focus {
                border-color: #667eea;
                background-color: #ffffff;

            }
        """)

        # تصفية حسب القسم
        department_label = QLabel("🏢 القسم:")
        department_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                font-weight: 600;
                color: #495057;
                min-width: 50px;
            }
        """)

        self.department_filter = QComboBox()
        self.department_filter.setStyleSheet("""
            QComboBox {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                padding: 6px 10px;
                border: 2px solid #dee2e6;
                border-radius: 6px;
                background-color: #ffffff;
                min-width: 120px;
            }
            QComboBox:focus {
                border-color: #27ae60;
            }
        """)

        # تصفية حسب العنوان الوظيفي
        job_title_label = QLabel("💼 المنصب:")
        job_title_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                font-weight: 600;
                color: #495057;
                min-width: 50px;
            }
        """)

        self.job_title_filter = QComboBox()
        self.job_title_filter.setStyleSheet("""
            QComboBox {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                padding: 6px 10px;
                border: 2px solid #dee2e6;
                border-radius: 6px;
                background-color: #ffffff;
                min-width: 120px;
            }
            QComboBox:focus {
                border-color: #27ae60;
            }
        """)

        # تصفية حسب حالة التوظيف
        status_label = QLabel("📊 الحالة:")
        status_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                font-weight: 600;
                color: #495057;
                min-width: 50px;
            }
        """)

        self.status_filter = QComboBox()
        self.status_filter.setStyleSheet("""
            QComboBox {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                padding: 6px 10px;
                border: 2px solid #dee2e6;
                border-radius: 6px;
                background-color: #ffffff;
                min-width: 100px;
            }
            QComboBox:focus {
                border-color: #27ae60;
            }
        """)

        # زر مسح التصفية
        self.clear_filter_btn = QPushButton("🗑️ مسح")
        self.clear_filter_btn.setStyleSheet("""
            QPushButton {
                font-size: 12px;
                font-weight: bold;
                color: white;
                background-color: #6c757d;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                min-width: 60px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)

        second_row.addWidget(search_label)
        second_row.addWidget(self.search_input)
        second_row.addWidget(department_label)
        second_row.addWidget(self.department_filter)
        second_row.addWidget(job_title_label)
        second_row.addWidget(self.job_title_filter)
        second_row.addWidget(status_label)
        second_row.addWidget(self.status_filter)
        second_row.addWidget(self.clear_filter_btn)

        search_layout.addLayout(first_row)
        search_layout.addLayout(second_row)

        layout.addWidget(search_frame)

        # تحميل بيانات التصفية
        self.load_filter_data()
        
    def create_employees_table(self, layout: QVBoxLayout):
        """إنشاء جدول الموظفين المحسن"""
        self.employees_table = QTableWidget()

        # إعداد الجدول مع أعمدة محسنة (بدون عمود الإجراءات)
        headers = [
            "الرقم الوظيفي", "الاسم الكامل", "القسم", "العنوان الوظيفي",
            "الراتب الأساسي", "تاريخ المباشرة", "حالة التوظيف"
        ]

        # أعمدة محسنة مع توزيع أفضل للمساحة الإضافية
        column_widths = [160, 300, 240, 240, 180, 180, 160]

        setup_table_widget(
            self.employees_table,
            headers,
            column_widths,
            sortable=True,
            selection_behavior="row"
        )

        # تحسين مظهر الجدول المحسن مع تأثيرات التحديد الواضحة
        self.employees_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: 3px solid #e9ecef;
                border-radius: 12px;
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 15px;
                gridline-color: #f1f3f4;
                selection-background-color: #667eea;
                selection-color: white;
                outline: none;
            }

            QTableWidget::item {
                padding: 12px;
                border-bottom: 1px solid #f1f3f4;
                text-align: center;
                color: #2c3e50;
                min-height: 25px;
                border: 1px solid transparent;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                            stop: 0 #667eea, stop: 1 #764ba2);
                color: white;
                font-weight: bold;
                border: 2px solid #5a67d8;
                border-radius: 6px;
            }

            QTableWidget::item:hover:!selected {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #f8f9fa, stop: 1 #e9ecef);
                border: 1px solid #667eea;
                border-radius: 4px;
            }

            QTableWidget::item:focus {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                            stop: 0 #667eea, stop: 1 #764ba2);
                color: white;
                font-weight: bold;
                border: 2px solid #5a67d8;
                border-radius: 6px;
                outline: none;
            }

            QHeaderView::section {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #667eea, stop: 1 #764ba2);
                color: white;
                font-weight: bold;
                font-size: 15px;
                padding: 15px;
                border: none;
                border-right: 1px solid rgba(255, 255, 255, 0.3);
                text-align: center;
            }

            QHeaderView::section:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #5a67d8, stop: 1 #6c5ce7);
            }
        """)

        # تمكين قائمة السياق
        self.employees_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.employees_table.customContextMenuRequested.connect(self.show_context_menu)

        # تمكين التحديد والتفاعل المحسن
        self.employees_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.employees_table.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.employees_table.setFocusPolicy(Qt.FocusPolicy.StrongFocus)

        # تحسين خصائص الجدول للتفاعل
        self.employees_table.setAlternatingRowColors(False)
        self.employees_table.setShowGrid(True)
        self.employees_table.setSortingEnabled(False)  # إيقاف الترتيب مؤقتاً لتجنب التداخل
        self.employees_table.setWordWrap(False)

        # تمكين التحديد بالنقر
        self.employees_table.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)
        self.employees_table.setTabKeyNavigation(True)

        # ربط إشارات التحديد
        self.employees_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.employees_table.cellClicked.connect(self.on_cell_clicked)
        self.employees_table.itemClicked.connect(self.on_item_clicked)

        layout.addWidget(self.employees_table)

    def create_action_buttons(self, layout: QVBoxLayout):
        """إنشاء شريط الأزرار المدمج والعملي"""
        # إطار الأزرار المدمج
        buttons_frame = QFrame()
        buttons_frame.setObjectName("compact_buttons_frame")
        buttons_frame.setStyleSheet("""
            QFrame#compact_buttons_frame {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #ffffff, stop: 1 #f8f9fa);
                border: 2px solid #e9ecef;
                border-radius: 10px;
                padding: 12px;
                margin: 10px 0px;
                max-height: 70px;
            }
        """)

        # تخطيط أفقي مدمج
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(8)
        buttons_layout.setContentsMargins(10, 5, 10, 5)

        # أزرار الإجراءات المدمجة
        self.add_btn = QPushButton("➕ إضافة")
        self.add_btn.setStyleSheet("""
            QPushButton {
                font-size: 13px;
                font-weight: bold;
                color: white;
                background-color: #28a745;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                min-width: 80px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
        """)

        self.edit_btn = QPushButton("✏️ تعديل")
        self.edit_btn.setEnabled(False)
        self.edit_btn.setToolTip("حدد موظفاً من الجدول أولاً لتفعيل زر التعديل")
        self.edit_btn.setStyleSheet("""
            QPushButton {
                font-size: 13px;
                font-weight: bold;
                color: white;
                background-color: #007bff;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                min-width: 70px;
                min-height: 35px;
            }
            QPushButton:hover:enabled {
                background-color: #0056b3;
            }
            QPushButton:pressed:enabled {
                background-color: #004085;
            }
            QPushButton:disabled {
                background-color: #6c757d;
                color: #adb5bd;
            }
        """)

        self.delete_btn = QPushButton("🗑️ حذف")
        self.delete_btn.setEnabled(False)
        self.delete_btn.setStyleSheet("""
            QPushButton {
                font-size: 13px;
                font-weight: bold;
                color: white;
                background-color: #dc3545;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                min-width: 60px;
                min-height: 35px;
            }
            QPushButton:hover:enabled {
                background-color: #c82333;
            }
            QPushButton:pressed:enabled {
                background-color: #bd2130;
            }
            QPushButton:disabled {
                background-color: #6c757d;
                color: #adb5bd;
            }
        """)

        self.view_details_btn = QPushButton("👁️ تفاصيل")
        self.view_details_btn.setEnabled(False)
        self.view_details_btn.setStyleSheet("""
            QPushButton {
                font-size: 13px;
                font-weight: bold;
                color: white;
                background-color: #17a2b8;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                min-width: 70px;
                min-height: 35px;
            }
            QPushButton:hover:enabled {
                background-color: #138496;
            }
            QPushButton:pressed:enabled {
                background-color: #117a8b;
            }
            QPushButton:disabled {
                background-color: #6c757d;
                color: #adb5bd;
            }
        """)

        self.refresh_btn = QPushButton("🔄 تحديث")
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                font-size: 13px;
                font-weight: bold;
                color: white;
                background-color: #6f42c1;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                min-width: 70px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
            QPushButton:pressed {
                background-color: #4e2a8e;
            }
        """)

        # ترتيب الأزرار المدمجة
        buttons_layout.addWidget(self.add_btn)
        buttons_layout.addWidget(self.edit_btn)
        buttons_layout.addWidget(self.delete_btn)
        buttons_layout.addWidget(self.view_details_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.refresh_btn)

        layout.addWidget(buttons_frame)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        # ربط إشارات البحث والتصفية
        self.search_input.textChanged.connect(self.filter_employees)
        self.department_filter.currentTextChanged.connect(self.filter_employees)
        self.job_title_filter.currentTextChanged.connect(self.filter_employees)
        self.status_filter.currentTextChanged.connect(self.filter_employees)
        self.clear_filter_btn.clicked.connect(self.clear_filters)
        
        # ربط إشارات الجدول
        self.employees_table.itemSelectionChanged.connect(self.on_selection_changed)
        # استخدام cellDoubleClicked فقط لتجنب التكرار
        self.employees_table.cellDoubleClicked.connect(self.on_cell_double_clicked)
        self.employees_table.cellClicked.connect(self.on_cell_clicked)
        
        # ربط إشارات الأزرار
        self.add_btn.clicked.connect(self.on_add_employee)
        self.edit_btn.clicked.connect(self.on_edit_employee)
        self.delete_btn.clicked.connect(self.on_delete_employee)
        self.view_details_btn.clicked.connect(self.on_view_details)
        self.refresh_btn.clicked.connect(self.load_employees)
        
    def load_filter_data(self):
        """تحميل بيانات التصفية"""
        try:
            with get_db_session_context() as session:
                # تحميل الأقسام
                departments = session.query(Department).filter_by(is_active=True).all()
                self.department_filter.clear()
                self.department_filter.addItem("جميع الأقسام", "")
                for dept in departments:
                    self.department_filter.addItem(dept.name, dept.id)
                
                # تحميل العناوين الوظيفية
                job_titles = session.query(JobTitle).filter_by(is_active=True).all()
                self.job_title_filter.clear()
                self.job_title_filter.addItem("جميع العناوين", "")
                for title in job_titles:
                    self.job_title_filter.addItem(title.title, title.id)
                
                # تحميل حالات التوظيف
                self.status_filter.clear()
                self.status_filter.addItem("جميع الحالات", "")
                for status in EmploymentStatus:
                    self.status_filter.addItem(status.value, status.name)
                    
        except Exception as e:
            show_message(self, "خطأ", f"فشل في تحميل بيانات التصفية: {e}", "error")
            
    def load_employees(self):
        """تحميل بيانات الموظفين مع الإحصائيات المحسنة"""
        try:
            with get_db_session_context() as session:
                employees = session.query(Employee).filter_by(is_active=True).all()

                self.employees_data = []

                for emp in employees:
                    row_data = [
                        emp.employee_number,
                        emp.full_name,
                        emp.department.name if emp.department else "",
                        emp.job_title.title if emp.job_title else "",
                        format_currency(emp.basic_salary),
                        format_date(emp.hire_date),
                        get_employment_status_display(emp.employment_status.name)
                    ]
                    # إضافة ID للاستخدام الداخلي
                    row_data.append(emp.id)
                    self.employees_data.append(row_data)

                # تطبيق التصفية
                self.filter_employees()

                # تحديث الإحصائيات
                total_employees = len(self.employees_data)
                active_count = len([emp for emp in self.employees_data if "نشط" in emp[6]])
                departments = session.query(Department).filter_by(is_active=True).all()
                total_departments = len(departments)

                self.employees_count_label.setText(f"📊 إجمالي: {total_employees}")
                self.active_employees_label.setText(f"✅ نشط: {active_count}")
                self.departments_count_label.setText(f"🏢 أقسام: {total_departments}")

        except Exception as e:
            show_message(self, "خطأ", f"فشل في تحميل بيانات الموظفين: {e}", "error")
            
    def filter_employees(self):
        """تصفية الموظفين باستخدام النظام المحسن"""
        search_text = self.search_input.text().strip()
        department_id = self.department_filter.currentData()
        job_title_id = self.job_title_filter.currentData()
        status = self.status_filter.currentData()

        # تحويل البيانات إلى قاموس للبحث المحسن
        data_dicts = []
        for row in self.employees_data:
            data_dict = {
                'employee_number': row[0],
                'full_name': row[1],
                'department': row[2],
                'job_title': row[3],
                'basic_salary': row[4],
                'hire_date': row[5],
                'employment_status': row[6],
                'id': row[7],
                'original_row': row
            }
            data_dicts.append(data_dict)

        # إنشاء بناء البحث
        search_builder = create_search_builder()

        # إضافة البحث النصي
        if search_text:
            search_builder.text_search(
                search_text,
                ['employee_number', 'full_name'],
                case_sensitive=False
            )

        # إضافة تصفية القسم
        if department_id and department_id != "":
            # البحث في قاعدة البيانات للحصول على اسم القسم
            try:
                with get_db_session_context() as session:
                    department = session.query(Department).filter_by(id=department_id).first()
                    if department:
                        search_builder.equals('department', department.name)
            except Exception:
                pass

        # إضافة تصفية العنوان الوظيفي
        if job_title_id and job_title_id != "":
            try:
                with get_db_session_context() as session:
                    job_title = session.query(JobTitle).filter_by(id=job_title_id).first()
                    if job_title:
                        search_builder.equals('job_title', job_title.title)
            except Exception:
                pass

        # إضافة تصفية الحالة
        if status and status != "":
            search_builder.contains('employment_status', get_employment_status_display(status))

        # تنفيذ البحث
        filtered_dicts = search_builder.execute(data_dicts)

        # استخراج الصفوف الأصلية
        self.filtered_data = [item['original_row'] for item in filtered_dicts]

        # تحديث الجدول
        self.update_table()
        
    def update_table(self):
        """تحديث الجدول المحسن"""
        # إزالة العمود الأخير (ID) من العرض
        display_data = [row[:-1] for row in self.filtered_data]
        populate_table(self.employees_table, display_data, editable=False)

        # تحسين خصائص العناصر للتحديد
        for row in range(self.employees_table.rowCount()):
            for col in range(self.employees_table.columnCount()):
                item = self.employees_table.item(row, col)
                if item:
                    if row < len(self.filtered_data):
                        # جعل العناصر قابلة للتحديد والتفاعل
                        item.setFlags(Qt.ItemFlag.ItemIsSelectable | Qt.ItemFlag.ItemIsEnabled)
                        # تحسين محاذاة النص
                        item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                        # إضافة خلفية طبيعية
                        item.setBackground(QColor(255, 255, 255))
                    else:
                        # الصفوف الفارغة غير قابلة للتحديد
                        item.setFlags(Qt.ItemFlag.NoItemFlags)
                        item.setBackground(QColor(255, 255, 255, 50))

        # لا نضيف صفوف فارغة لتجنب مشاكل التحديد
        # الجدول سيعرض البيانات الفعلية فقط

        # تحديث عداد النتائج المفلترة
        filtered_count = len(self.filtered_data)
        total_count = len(self.employees_data)

        if filtered_count != total_count:
            self.employees_count_label.setText(
                f"📊 إجمالي الموظفين: {total_count} (المعروض: {filtered_count})"
            )

        # التأكد من تحديث شريط التمرير
        self.employees_table.viewport().update()
        self.employees_table.repaint()
        
    def clear_filters(self):
        """مسح جميع التصفيات"""
        self.search_input.clear()
        self.department_filter.setCurrentIndex(0)
        self.job_title_filter.setCurrentIndex(0)
        self.status_filter.setCurrentIndex(0)
        
    def on_selection_changed(self):
        """معالج تغيير التحديد"""
        row = self.employees_table.currentRow()

        # التحقق من أن الصف المحدد ضمن البيانات الفعلية
        has_valid_selection = 0 <= row < len(self.filtered_data)

        # التحقق من أن العنصر المحدد ليس فارغاً
        if has_valid_selection:
            current_item = self.employees_table.item(row, 0)
            if current_item and current_item.text().strip():
                has_selection = True
            else:
                has_selection = False
        else:
            has_selection = False

        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)
        self.view_details_btn.setEnabled(has_selection)

        # تحديث تلميح الزر
        if has_selection:
            employee_name = self.filtered_data[row][1]
            self.edit_btn.setToolTip(f"تعديل بيانات الموظف: {employee_name}")
            self.delete_btn.setToolTip(f"حذف الموظف: {employee_name}")
            self.view_details_btn.setToolTip(f"عرض تفاصيل الموظف: {employee_name}")
        else:
            self.edit_btn.setToolTip("حدد موظفاً من الجدول أولاً لتفعيل زر التعديل")
            self.delete_btn.setToolTip("حدد موظفاً من الجدول أولاً لتفعيل زر الحذف")
            self.view_details_btn.setToolTip("حدد موظفاً من الجدول أولاً لتفعيل زر عرض التفاصيل")

        if has_selection:
            employee_id = self.filtered_data[row][-1]  # آخر عنصر هو ID
            self.employee_selected.emit(employee_id)

            # تطبيق التأثير البصري للتحديد
            self.highlight_selected_row(row)

    def on_cell_clicked(self, row, column):
        """معالج النقر على خلية"""
        # التأكد من أن الصف ضمن البيانات الفعلية
        if 0 <= row < len(self.filtered_data):
            # مسح التحديد السابق
            self.employees_table.clearSelection()

            # تحديد الصف كاملاً
            self.employees_table.selectRow(row)

            # التأكد من أن الصف محدد
            self.employees_table.setCurrentCell(row, 0)

            # إضافة تأثير بصري للتحديد
            self.highlight_selected_row(row)

            # تحديث حالة الأزرار
            self.on_selection_changed()

    def on_item_clicked(self, item):
        """معالج النقر على عنصر"""
        if item:
            row = item.row()
            print(f"تم النقر على العنصر في الصف: {row}")

            # التأكد من أن الصف ضمن البيانات الفعلية
            if 0 <= row < len(self.filtered_data):
                # تحديد الصف
                self.employees_table.selectRow(row)

                # إضافة تأثير بصري للتحديد
                self.highlight_selected_row(row)

                # تحديث حالة الأزرار
                self.on_selection_changed()

    def highlight_selected_row(self, row):
        """إضافة تأثير بصري للصف المحدد"""
        try:
            # إزالة التمييز من جميع الصفوف
            for r in range(self.employees_table.rowCount()):
                for c in range(self.employees_table.columnCount()):
                    item = self.employees_table.item(r, c)
                    if item:
                        # إعادة تعيين الخلفية الافتراضية
                        if r < len(self.filtered_data):
                            item.setBackground(QColor(255, 255, 255))
                        else:
                            item.setBackground(QColor(255, 255, 255, 50))

            # تمييز الصف المحدد
            if 0 <= row < len(self.filtered_data):
                for c in range(self.employees_table.columnCount()):
                    item = self.employees_table.item(row, c)
                    if item:
                        # تطبيق لون التحديد
                        item.setBackground(QColor(102, 126, 234, 100))  # لون أزرق شفاف

        except Exception as e:
            print(f"خطأ في تمييز الصف: {e}")
                


    def on_cell_double_clicked(self, row, column):
        """معالج النقر المزدوج على خلية"""
        # التأكد من أن الصف ضمن البيانات الفعلية
        if 0 <= row < len(self.filtered_data):
            # تحديد الصف أولاً
            self.employees_table.selectRow(row)
            self.employees_table.setCurrentCell(row, 0)

            # تحديث التحديد
            self.on_selection_changed()

            # عرض التفاصيل مباشرة
            self.on_view_details()

    def show_context_menu(self, position):
        """عرض قائمة السياق المحسنة للجدول"""
        # التحقق من وجود عنصر في الموضع المحدد
        item = self.employees_table.itemAt(position)

        # إنشاء قائمة السياق
        from PySide6.QtWidgets import QMenu
        context_menu = QMenu(self)

        # تطبيق تصميم محسن للقائمة
        context_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #ffffff, stop: 1 #f8f9fa);
                border: 3px solid #667eea;
                border-radius: 12px;
                padding: 8px;
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                min-width: 280px;
            }

            QMenu::item {
                background-color: transparent;
                padding: 12px 20px;
                margin: 2px;
                border-radius: 8px;
                color: #2c3e50;
                font-weight: 600;
                min-height: 20px;
            }

            QMenu::item:selected {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                            stop: 0 #667eea, stop: 1 #764ba2);
                color: white;
                font-weight: bold;
            }

            QMenu::item:disabled {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #e9ecef, stop: 1 #dee2e6);
                color: #495057;
                font-weight: bold;
                font-size: 15px;
                text-align: center;
                border: 2px solid #adb5bd;
                border-radius: 10px;
                margin: 5px;
                padding: 15px;
            }

            QMenu::separator {
                height: 2px;
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                            stop: 0 #dee2e6, stop: 1 #adb5bd);
                margin: 8px 15px;
                border-radius: 1px;
            }
        """)

        # الحصول على الصف المحدد من الموضع
        row = self.employees_table.rowAt(position.y())

        # التأكد من أن الصف صالح وليس من الصفوف الفارغة المضافة
        if row >= 0 and row < len(self.filtered_data):
            # تحديد الصف
            self.employees_table.selectRow(row)
            has_selection = True
        else:
            has_selection = False

        if has_selection:
            employee_data = self.filtered_data[row]
            employee_name = employee_data[1]
            employee_number = employee_data[0]
            department = employee_data[2]
            job_title = employee_data[3]

            # إضافة عنوان القائمة المحسن
            title_text = f"👤 {employee_name}\n🆔 {employee_number} • 🏢 {department}"
            title_action = context_menu.addAction(title_text)
            title_action.setEnabled(False)
            context_menu.addSeparator()

            # إضافة الإجراءات الأساسية
            view_action = context_menu.addAction("👁️ عرض التفاصيل الكاملة")
            view_action.setToolTip("عرض جميع معلومات الموظف وتفاصيله")
            view_action.triggered.connect(self.on_view_details)

            edit_action = context_menu.addAction("✏️ تعديل بيانات الموظف")
            edit_action.setToolTip("تعديل المعلومات الشخصية والوظيفية")
            edit_action.triggered.connect(self.on_edit_employee)

            # إضافة إجراءات إضافية
            context_menu.addSeparator()

            # إجراءات مالية
            financial_action = context_menu.addAction("💰 المعاملات المالية")
            financial_action.setToolTip("عرض وإدارة المعاملات المالية للموظف")
            financial_action.triggered.connect(lambda: self.on_financial_operations(row))

            salary_action = context_menu.addAction("📊 سجل الرواتب")
            salary_action.setToolTip("عرض تاريخ رواتب الموظف")
            salary_action.triggered.connect(lambda: self.on_salary_history(row))

            # إجراءات إضافية
            context_menu.addSeparator()

            copy_action = context_menu.addAction("📋 نسخ معلومات الموظف")
            copy_action.setToolTip("نسخ معلومات الموظف إلى الحافظة")
            copy_action.triggered.connect(lambda: self.copy_employee_info(row))

            context_menu.addSeparator()

            # إجراءات خطيرة
            delete_action = context_menu.addAction("🗑️ حذف الموظف")
            delete_action.setToolTip("حذف الموظف نهائياً من النظام")
            delete_action.triggered.connect(self.on_delete_employee)

        else:
            # قائمة عامة عند عدم التحديد
            no_selection_action = context_menu.addAction("⚠️ لم يتم تحديد موظف")
            no_selection_action.setEnabled(False)
            context_menu.addSeparator()

            add_action = context_menu.addAction("➕ إضافة موظف جديد")
            add_action.setToolTip("إضافة موظف جديد للنظام")
            add_action.triggered.connect(self.on_add_employee)

            refresh_action = context_menu.addAction("🔄 تحديث القائمة")
            refresh_action.setToolTip("إعادة تحميل بيانات الموظفين")
            refresh_action.triggered.connect(self.load_employees)

        # عرض القائمة
        context_menu.exec(self.employees_table.mapToGlobal(position))

    def on_add_employee(self):
        """معالج إضافة موظف"""
        self.add_employee_requested.emit()
        
    def on_edit_employee(self):
        """معالج تعديل موظف"""
        row = self.employees_table.currentRow()

        if 0 <= row < len(self.filtered_data):
            employee_id = self.filtered_data[row][-1]
            self.edit_employee_requested.emit(employee_id)
        else:
            from ..utils import show_message
            show_message(self, "تنبيه", "يرجى تحديد موظف من القائمة أولاً", "warning")
            
    def on_delete_employee(self):
        """معالج حذف موظف"""
        row = self.employees_table.currentRow()
        if 0 <= row < len(self.filtered_data):
            employee_name = self.filtered_data[row][1]
            
            if show_confirmation(
                self,
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف الموظف '{employee_name}'؟\n\nهذا الإجراء لا يمكن التراجع عنه."
            ):
                employee_id = self.filtered_data[row][-1]
                self.delete_employee(employee_id)
                
    def on_view_details(self):
        """معالج عرض التفاصيل"""
        row = self.employees_table.currentRow()
        print(f"عرض التفاصيل: الصف الحالي = {row}")
        print(f"عدد البيانات المفلترة: {len(self.filtered_data)}")

        if 0 <= row < len(self.filtered_data):
            employee_id = self.filtered_data[row][-1]
            employee_name = self.filtered_data[row][1]
            print(f"عرض تفاصيل الموظف: {employee_name} (ID: {employee_id})")

            try:
                # استيراد نافذة التفاصيل
                from .employee_details import EmployeeDetailsDialog

                # عرض نافذة التفاصيل
                dialog = EmployeeDetailsDialog(employee_id, self)
                dialog.exec()

            except Exception as e:
                print(f"❌ خطأ في عرض نافذة التفاصيل: {e}")
                import traceback
                traceback.print_exc()

                # عرض رسالة خطأ للمستخدم
                from ..utils import show_message
                show_message(self, "خطأ", f"فشل في عرض تفاصيل الموظف: {e}", "error")
        else:
            print(f"لا يوجد تحديد صالح: الصف = {row}")
            from ..utils import show_message
            show_message(self, "تنبيه", "يرجى تحديد موظف من القائمة أولاً", "warning")
            
    def delete_employee(self, employee_id: int):
        """حذف موظف"""
        try:
            with get_db_session_context() as session:
                employee = session.query(Employee).filter_by(id=employee_id).first()
                if employee:
                    employee.soft_delete()
                    session.commit()
                    
                    show_message(self, "نجح", "تم حذف الموظف بنجاح", "information")
                    self.load_employees()
                else:
                    show_message(self, "خطأ", "الموظف غير موجود", "error")
                    
        except Exception as e:
            show_message(self, "خطأ", f"فشل في حذف الموظف: {e}", "error")
            
    def get_selected_employee_id(self) -> int:
        """الحصول على معرف الموظف المحدد"""
        row = self.employees_table.currentRow()
        if 0 <= row < len(self.filtered_data):
            return self.filtered_data[row][-1]
        return -1

    def on_financial_operations(self, row: int):
        """معالج المعاملات المالية"""
        if 0 <= row < len(self.filtered_data):
            employee_id = self.filtered_data[row][-1]
            employee_name = self.filtered_data[row][1]

            try:
                # يمكن إضافة نافذة المعاملات المالية هنا
                from ..utils import show_message
                show_message(
                    self,
                    "معلومات",
                    f"🚧 نافذة المعاملات المالية للموظف '{employee_name}' قيد التطوير\n\nسيتم إضافتها في التحديث القادم",
                    "information"
                )
            except Exception as e:
                from ..utils import show_message
                show_message(self, "خطأ", f"فشل في فتح المعاملات المالية: {e}", "error")

    def on_salary_history(self, row: int):
        """معالج سجل الرواتب"""
        if 0 <= row < len(self.filtered_data):
            employee_id = self.filtered_data[row][-1]
            employee_name = self.filtered_data[row][1]

            try:
                # يمكن إضافة نافذة سجل الرواتب هنا
                from ..utils import show_message
                show_message(
                    self,
                    "معلومات",
                    f"🚧 نافذة سجل الرواتب للموظف '{employee_name}' قيد التطوير\n\nسيتم إضافتها في التحديث القادم",
                    "information"
                )
            except Exception as e:
                from ..utils import show_message
                show_message(self, "خطأ", f"فشل في فتح سجل الرواتب: {e}", "error")

    def copy_employee_info(self, row: int):
        """نسخ معلومات الموظف إلى الحافظة"""
        if 0 <= row < len(self.filtered_data):
            try:
                from PySide6.QtGui import QClipboard
                from PySide6.QtWidgets import QApplication

                employee_data = self.filtered_data[row]
                employee_info = f"""معلومات الموظف:
👤 الاسم: {employee_data[1]}
🆔 الرقم الوظيفي: {employee_data[0]}
🏢 القسم: {employee_data[2]}
💼 المنصب: {employee_data[3]}
💰 الراتب: {employee_data[4]}
📅 تاريخ المباشرة: {employee_data[5]}
📊 الحالة: {employee_data[6]}"""

                clipboard = QApplication.clipboard()
                clipboard.setText(employee_info)

                from ..utils import show_message
                show_message(
                    self,
                    "نجح",
                    f"✅ تم نسخ معلومات الموظف '{employee_data[1]}' إلى الحافظة",
                    "information"
                )
            except Exception as e:
                from ..utils import show_message
                show_message(self, "خطأ", f"فشل في نسخ المعلومات: {e}", "error")
