#!/usr/bin/env python3
"""
ملف تشغيل نظام إدارة شؤون الموظفين
HR Management System Launcher
"""

import sys
import os
import logging
from pathlib import Path
from PySide6.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QPixmap, QFont

# إضافة مجلد src إلى المسار
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.config import ensure_directories, get_config
from src.database import db_manager
from src.views import MainWindow


def apply_global_font_style(app, font_size):
    """تطبيق نمط الخط العام على جميع العناصر"""
    # تطبيق الخط على التطبيق مباشرة
    from PySide6.QtGui import QFont
    font = QFont("Arial", font_size)
    font.setStyleHint(QFont.SansSerif)
    app.setFont(font)

    # تطبيق على جميع العناصر الموجودة
    for widget in app.allWidgets():
        if widget and hasattr(widget, 'setFont'):
            widget.setFont(font)


def setup_logging():
    """إعداد نظام التسجيل"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / "hr_system.log", encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )


def setup_application():
    """إعداد التطبيق"""
    app = QApplication(sys.argv)
    
    # إعداد معلومات التطبيق
    app_config = get_config("app")
    app.setApplicationName(app_config["name"])
    app.setApplicationVersion(app_config["version"])
    app.setOrganizationName("HR Management System")
    
    # إعداد نظام المظهر المحسن
    from src.utils.appearance_manager import get_appearance_manager
    from src.utils.theme_manager import ThemeManager

    appearance_manager = get_appearance_manager()
    theme_manager = ThemeManager()

    # تحميل إعدادات المظهر المحفوظة وتطبيقها
    appearance_manager.load_settings()

    # تطبيق الوضع الليلي المحسن كافتراضي
    print("🌙 تطبيق الوضع الليلي المحسن...")
    success = theme_manager.set_theme("dark")
    if success:
        print("✅ تم تطبيق الوضع الليلي المحسن بنجاح")
    else:
        print("⚠️ فشل في تطبيق الوضع الليلي، استخدام الثيم المحفوظ...")
        theme_manager.apply_current_theme()
    
    # تطبيق التخطيط من اليمين إلى اليسار
    app.setLayoutDirection(Qt.RightToLeft)
    
    return app


def show_splash_screen():
    """عرض شاشة البداية"""
    splash = QSplashScreen()
    splash.showMessage(
        "جاري تحميل نظام إدارة شؤون الموظفين...",
        Qt.AlignBottom | Qt.AlignCenter,
        Qt.white
    )
    splash.show()
    return splash


def initialize_database():
    """تهيئة قاعدة البيانات"""
    try:
        # تهيئة مدير قاعدة البيانات
        db_manager.initialize()
        
        # اختبار الاتصال
        if not db_manager.test_connection():
            # في حالة فشل PostgreSQL، التبديل إلى SQLite
            logging.warning("فشل الاتصال بـ PostgreSQL، التبديل إلى SQLite")
            from src.config import update_config
            update_config("database", "type", "sqlite")
            update_config("database", "database", "hr_system.db")
            db_manager.initialize()
        
        # إنشاء الجداول إذا لم تكن موجودة
        db_manager.create_tables()
        
        logging.info("تم تهيئة قاعدة البيانات بنجاح")
        return True
        
    except Exception as e:
        logging.error(f"خطأ في تهيئة قاعدة البيانات: {e}")
        return False


def show_database_error():
    """عرض رسالة خطأ قاعدة البيانات"""
    msg = QMessageBox()
    msg.setIcon(QMessageBox.Critical)
    msg.setWindowTitle("خطأ في قاعدة البيانات")
    msg.setText("فشل في الاتصال بقاعدة البيانات")
    msg.setInformativeText(
        "تأكد من:\n"
        "1. تشغيل خادم PostgreSQL\n"
        "2. صحة إعدادات الاتصال\n"
        "3. وجود قاعدة البيانات المطلوبة\n\n"
        "سيتم استخدام SQLite كبديل"
    )
    msg.setStandardButtons(QMessageBox.Ok)
    msg.exec()


def load_styles(app: QApplication):
    """تحميل ملفات الأنماط الأساسية مع دعم الثيمات"""
    try:
        from src.utils.theme_manager import ThemeManager

        # إنشاء مدير الثيمات
        theme_manager = ThemeManager()

        # تطبيق الوضع الليلي المحسن كافتراضي
        print("🌙 تحميل الوضع الليلي المحسن...")
        success = theme_manager.set_theme("dark")

        if success:
            logging.info("تم تحميل الوضع الليلي المحسن")
            print("✅ تم تحميل الوضع الليلي المحسن بنجاح")
        else:
            # في حالة فشل تطبيق الوضع الليلي، جرب الثيم المحفوظ
            if not theme_manager.apply_current_theme():
                # في حالة فشل كل شيء، استخدم الثيم الافتراضي
                styles_dir = Path("src/styles")
                main_style_file = styles_dir / "main.qss"

                if main_style_file.exists():
                    with open(main_style_file, 'r', encoding='utf-8') as f:
                        style_content = f.read()
                        app.setStyleSheet(style_content)
                    logging.info("تم تحميل الثيم الاحتياطي")
                    print("⚠️ تم تحميل الثيم الاحتياطي")
                else:
                    logging.warning("ملف الثيم الافتراضي غير موجود")
                    print("❌ لم يتم العثور على أي ثيم")
            else:
                logging.info(f"تم تحميل الثيم المحفوظ: {theme_manager.get_current_theme()}")
                print(f"✅ تم تحميل الثيم المحفوظ: {theme_manager.get_current_theme()}")

    except Exception as e:
        logging.warning(f"فشل في تحميل ملفات الأنماط: {e}")
        # محاولة تحميل الثيم الافتراضي كحل احتياطي
        try:
            styles_dir = Path("src/styles")
            main_style_file = styles_dir / "main.qss"
            if main_style_file.exists():
                with open(main_style_file, 'r', encoding='utf-8') as f:
                    app.setStyleSheet(f.read())
                logging.info("تم تحميل الثيم الاحتياطي")
        except:
            logging.error("فشل في تحميل أي ثيم")


class HRApplication:
    """فئة التطبيق الرئيسية"""
    
    def __init__(self):
        self.app = None
        self.main_window = None
        self.splash = None
        
    def run(self):
        """تشغيل التطبيق"""
        try:
            # إعداد نظام التسجيل
            setup_logging()
            logging.info("بدء تشغيل نظام إدارة شؤون الموظفين")
            
            # إنشاء التطبيق
            self.app = setup_application()
            
            # التأكد من وجود المجلدات المطلوبة
            ensure_directories()
            
            # عرض شاشة البداية
            self.splash = show_splash_screen()
            self.app.processEvents()
            
            # تهيئة قاعدة البيانات
            self.splash.showMessage("جاري تهيئة قاعدة البيانات...", Qt.AlignBottom | Qt.AlignCenter)
            self.app.processEvents()
            
            if not initialize_database():
                self.splash.close()
                show_database_error()
                # محاولة أخيرة مع SQLite
                try:
                    from src.config import update_config
                    update_config("database", "type", "sqlite")
                    update_config("database", "database", "hr_system.db")
                    if not initialize_database():
                        return 1
                except:
                    return 1
            
            # تحميل الأنماط
            self.splash.showMessage("جاري تحميل الواجهة...", Qt.AlignBottom | Qt.AlignCenter)
            self.app.processEvents()
            load_styles(self.app)
            
            # إنشاء النافذة الرئيسية
            self.main_window = MainWindow()
            
            # إخفاء شاشة البداية وعرض النافذة الرئيسية
            QTimer.singleShot(1000, self.show_main_window)
            
            # تشغيل حلقة الأحداث
            return self.app.exec()
            
        except Exception as e:
            logging.error(f"خطأ في تشغيل التطبيق: {e}")
            if self.splash:
                self.splash.close()
            
            # عرض رسالة خطأ
            if self.app:
                msg = QMessageBox()
                msg.setIcon(QMessageBox.Critical)
                msg.setWindowTitle("خطأ في التطبيق")
                msg.setText(f"حدث خطأ في تشغيل التطبيق:\n{str(e)}")
                msg.exec()
            
            return 1
    
    def show_main_window(self):
        """عرض النافذة الرئيسية"""
        if self.splash:
            self.splash.close()
        
        if self.main_window:
            self.main_window.show()
            logging.info("تم تشغيل التطبيق بنجاح")


def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تشغيل نظام إدارة شؤون الموظفين...")
    
    app = HRApplication()
    exit_code = app.run()
    
    if exit_code == 0:
        print("✅ تم إغلاق النظام بنجاح")
    else:
        print("❌ تم إغلاق النظام مع أخطاء")
    
    return exit_code


if __name__ == "__main__":
    sys.exit(main())
