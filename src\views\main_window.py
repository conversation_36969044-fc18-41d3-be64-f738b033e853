"""
النافذة الرئيسية للنظام - بدون عنوان رئيسي
Main Window for HR System - Without Header
"""

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QHBoxLayout, QVBoxLayout, QStackedWidget,
    QLabel, QFrame, QSplitter, QApplication
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QIcon

from ..widgets.enhanced_dashboard import EnhancedDashboard
from ..views.employees_view import EmployeesView
from ..views.departments_view import DepartmentsView
from ..views.job_titles_view import JobTitlesView
from ..views.financial_view import FinancialView
from ..views.salaries_view import SalariesView
from ..views.reports_view import ReportsView
from ..views.backup_view import BackupView
from ..views.audit_view import AuditView
from ..views.settings_view import SettingsView
from ..utils.theme_manager import ThemeManager

class MainWindow(QMainWindow):
    """النافذة الرئيسية للنظام"""
    
    # إشارات
    page_changed = Signal(str)
    
    def __init__(self):
        super().__init__()
        self.pages = {}
        self.current_page = "dashboard"
        self.header = None  # لا يوجد عنوان رئيسي

        # إنشاء مدير الثيمات
        self.theme_manager = ThemeManager()

        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("نظام إدارة الموارد البشرية")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        # إنشاء الواجهة بدون عنوان رئيسي
        self.create_main_content()
        self.create_status_bar()
        
        # تطبيق الأنماط
        self.apply_styles()
        
        # إعداد النافذة لتفتح في وضع ملء الشاشة
        self.showMaximized()
        
    def create_header(self):
        """إزالة العنوان الرئيسي الكامل"""
        # لا يتم إنشاء أي عنوان رئيسي
        self.header = None

    def create_simple_sidebar(self):
        """إنشاء شريط جانبي محسن بتصميم عصري"""
        from PySide6.QtWidgets import QPushButton, QScrollArea
        from PySide6.QtCore import QPropertyAnimation, QEasingCurve

        # الحاوية الرئيسية للشريط الجانبي
        sidebar = QFrame()
        sidebar.setObjectName("modernSidebar")
        sidebar.setStyleSheet("""
            QFrame#modernSidebar {
                background-color: #f8f9fa;
                border: none;
                border-right: 4px solid qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3498db, stop:0.3 #2980b9, stop:0.7 #1abc9c, stop:1 #3498db);
            }
        """)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(sidebar)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # منطقة الشعار والعنوان بتصميم مشابه لإدارة الموظفين
        header_section = QFrame()
        header_section.setObjectName("sidebar_header_frame")
        header_section.setFixedHeight(120)
        header_section.setStyleSheet("""
            QFrame#sidebar_header_frame {
                background-color: #d5f4e6;
                border: 2px solid #27ae60;
                border-radius: 10px;
                margin: 5px;
            }
        """)

        header_layout = QVBoxLayout(header_section)
        header_layout.setContentsMargins(15, 15, 15, 15)
        header_layout.setSpacing(8)

        # العنوان الرئيسي
        main_title = QLabel("💼 برنامج إدارة الموظفين")
        main_title.setAlignment(Qt.AlignCenter)
        main_title.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                background-color: transparent;
                padding: 8px;
                margin: 0px;
            }
        """)

        header_layout.addWidget(main_title)

        # منطقة التمرير للأزرار
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                background: transparent;
                border: none;
            }
            QScrollBar:vertical {
                background: rgba(255, 255, 255, 0.1);
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background: #3498db;
                border-radius: 4px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: #2980b9;
            }
        """)

        # الحاوية الداخلية للأزرار
        buttons_container = QWidget()
        buttons_layout = QVBoxLayout(buttons_container)
        buttons_layout.setContentsMargins(15, 20, 15, 20)
        buttons_layout.setSpacing(8)

        # أزرار القوائم المحسنة
        buttons = [
            ("📊", "لوحة التحكم", "dashboard", "#e74c3c"),
            ("👥", "الموظفين", "employees", "#3498db"),
            ("🏢", "الأقسام", "departments", "#9b59b6"),
            ("💼", "المناصب", "job_titles", "#f39c12"),
            ("💰", "الرواتب", "salaries", "#27ae60"),
            ("💳", "المعاملات المالية", "financial", "#e67e22"),
            ("📊", "التقارير", "reports", "#1abc9c"),
            ("💾", "النسخ الاحتياطي", "backup", "#34495e"),
            ("🔍", "تدقيق الأنشطة", "audit", "#8e44ad"),
            ("⚙️", "الإعدادات", "settings", "#95a5a6"),
        ]

        self.sidebar_buttons = {}  # لحفظ مراجع الأزرار

        for icon, text, page_id, color in buttons:
            btn = self.create_modern_button(icon, text, page_id, color)
            self.sidebar_buttons[page_id] = btn
            buttons_layout.addWidget(btn)

        # مساحة مرنة في النهاية
        buttons_layout.addStretch()

        # تجميع العناصر
        scroll_area.setWidget(buttons_container)
        main_layout.addWidget(header_section)
        main_layout.addWidget(scroll_area, 1)

        return sidebar

    def create_modern_button(self, icon, text, page_id, color):
        """إنشاء زر حديث مع تأثيرات بصرية"""
        from PySide6.QtWidgets import QPushButton

        btn = QPushButton()
        btn.setMinimumHeight(55)
        btn.setMaximumHeight(55)
        btn.setObjectName(f"btn_{page_id}")

        # تعيين النص مع الأيقونة
        btn.setText(f"  {icon}   {text}")

        # تطبيق الأنماط المحسنة
        btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #ffffff;
                color: #2c3e50;
                border: 2px solid #dee2e6;
                border-radius: 12px;
                padding: 12px 16px;
                text-align: left;
                font-family: 'Segoe UI Variable Text', 'Segoe UI', sans-serif;
                font-size: 14px;
                font-weight: 600;
                letter-spacing: 0.5px;
            }}
            QPushButton:hover {{
                background-color: {color};
                color: #ffffff;
                border: 2px solid {color};
                margin-left: 5px;
            }}
            QPushButton:pressed {{
                background-color: rgba({self.hex_to_rgb(color)}, 0.8);
                border: 2px solid {color};
                color: #ffffff;
            }}
            QPushButton:checked {{
                background-color: {color};
                color: #ffffff;
                border: 2px solid {color};
                font-weight: 700;
            }}
        """)

        # ربط الإشارة
        btn.clicked.connect(lambda: self.on_sidebar_button_clicked(page_id))

        # جعل الزر قابل للتحديد
        btn.setCheckable(True)

        return btn

    def hex_to_rgb(self, hex_color):
        """تحويل اللون من hex إلى rgb"""
        hex_color = hex_color.lstrip('#')
        return ', '.join(str(int(hex_color[i:i+2], 16)) for i in (0, 2, 4))



    def on_sidebar_button_clicked(self, page_id):
        """معالج النقر على أزرار الشريط الجانبي"""
        # إلغاء تحديد جميع الأزرار
        for btn_id, btn in self.sidebar_buttons.items():
            btn.setChecked(btn_id == page_id)

        # عرض الصفحة المطلوبة
        self.show_page(page_id)

    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        # إنشاء الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # إنشاء المحتوى الأساسي
        self.main_content = QSplitter(Qt.Horizontal)
        
        # إنشاء الشريط الجانبي المحسن
        self.sidebar = self.create_simple_sidebar()
        sidebar_width = 320  # زيادة العرض للتصميم الجديد
        self.sidebar.setFixedWidth(sidebar_width)
        
        # إنشاء منطقة المحتوى
        self.content_area = QStackedWidget()
        self.content_area.setObjectName("content_area")
        
        # إضافة الصفحات
        self.setup_pages()
        
        # إضافة العناصر إلى المقسم
        self.main_content.addWidget(self.sidebar)
        self.main_content.addWidget(self.content_area)
        
        # تعيين النسب مع العرض الجديد
        self.main_content.setSizes([
            sidebar_width,
            self.width() - sidebar_width
        ])
        
        # إضافة المحتوى إلى التخطيط الرئيسي
        main_layout.addWidget(self.main_content)
        
    def setup_pages(self):
        """إعداد الصفحات"""
        # لوحة التحكم المحسنة
        from ..widgets.enhanced_dashboard import EnhancedDashboard
        self.dashboard = EnhancedDashboard()
        self.add_page("dashboard", self.dashboard, "لوحة التحكم")

        # إدارة الموظفين
        self.employees_view = EmployeesView()
        self.add_page("employees", self.employees_view, "إدارة الموظفين")

        # إدارة الأقسام
        self.departments_view = DepartmentsView()
        self.add_page("departments", self.departments_view, "الأقسام")

        # إدارة العناوين الوظيفية
        self.job_titles_view = JobTitlesView()
        self.add_page("job_titles", self.job_titles_view, "العناوين الوظيفية")

        # إدارة المعاملات المالية
        self.financial_view = FinancialView()
        self.add_page("financial", self.financial_view, "المعاملات المالية")

        # إدارة الرواتب
        self.salaries_view = SalariesView()
        self.add_page("salaries", self.salaries_view, "الرواتب")

        # التقارير
        self.reports_view = ReportsView()
        self.add_page("reports", self.reports_view, "التقارير")

        # النسخ الاحتياطي
        self.backup_view = BackupView()
        self.add_page("backup", self.backup_view, "النسخ الاحتياطي")

        # تدقيق الأنشطة
        self.audit_view = AuditView()
        self.add_page("audit", self.audit_view, "تدقيق الأنشطة")

        # الإعدادات
        self.settings_view = SettingsView()
        self.add_page("settings", self.settings_view, "الإعدادات")

        # عرض لوحة التحكم افتراضياً
        self.show_page("dashboard")

        # تحديد زر لوحة التحكم كافتراضي
        if hasattr(self, 'sidebar_buttons') and "dashboard" in self.sidebar_buttons:
            self.sidebar_buttons["dashboard"].setChecked(True)
        
    def add_page(self, page_id: str, widget: QWidget, title: str):
        """إضافة صفحة جديدة"""
        self.pages[page_id] = {
            "widget": widget,
            "title": title,
            "index": self.content_area.addWidget(widget)
        }
        
    def create_status_bar(self):
        """إنشاء شريط الحالة المحسن"""
        status_bar = self.statusBar()
        status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #f8f9fa;
                border-top: 1px solid #dee2e6;
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                color: #6c757d;
                padding: 5px;
            }
        """)

        # إضافة النص الإرشادي الافتراضي
        status_bar.showMessage("💡 نصيحة: انقر بالزر الأيمن على أي موظف للوصول للإجراءات السريعة | الصفحة الحالية: لوحة التحكم")
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        # ربط إشارة تغيير الثيم
        self.theme_manager.theme_changed.connect(self.on_theme_changed)

    def on_theme_changed(self, theme_name: str):
        """معالج تغيير الثيم"""
        # تحديث شريط الحالة
        theme_display = "الوضع الليلي" if theme_name == "dark" else "الوضع النهاري"
        self.statusBar().showMessage(f"تم تطبيق {theme_display}")

    def toggle_theme(self):
        """تبديل الثيم"""
        return self.theme_manager.toggle_theme()

    def set_theme(self, theme_name: str):
        """تعيين ثيم محدد"""
        return self.theme_manager.set_theme(theme_name)

    def get_current_theme(self):
        """الحصول على الثيم الحالي"""
        return self.theme_manager.get_current_theme()
        
    def show_page(self, page_id: str):
        """عرض صفحة معينة"""
        if page_id in self.pages:
            page_info = self.pages[page_id]
            self.content_area.setCurrentIndex(page_info["index"])
            self.current_page = page_id

            # تحديث شريط الحالة
            status_message = f"الصفحة الحالية: {page_info['title']}"
            self.statusBar().showMessage(status_message)

            # إرسال إشارة تغيير الصفحة
            self.page_changed.emit(page_id)
            
    def get_current_page(self) -> str:
        """الحصول على الصفحة الحالية"""
        return self.current_page



    def apply_styles(self):
        """تطبيق الأنماط"""
        # تطبيق أنماط عامة للنافذة
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
        """)

    def closeEvent(self, event):
        """حدث إغلاق النافذة"""
        # يمكن إضافة منطق الحفظ والتنظيف هنا
        event.accept()
