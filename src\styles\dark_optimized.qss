/*
🌙 ملف الوضع الليلي المحسن والمتوافق
Optimized Dark Mode Stylesheet - Compatible with Qt
*/

/* الإعدادات العامة */
* {
    color: #e2e8f0;
    font-family: 'Segoe UI', '<PERSON>o', 'Arial', sans-serif;
    background-color: #1a1a2e;
    selection-background-color: #667eea;
    selection-color: #ffffff;
}

/* النافذة الرئيسية */
QMainWindow {
    background-color: #1a1a2e;
    border: 1px solid #2d3748;
}

/* الشريط الجانبي */
QWidget#sidebar {
    background-color: #16213e;
    border-right: 2px solid #2d3748;
}

QLabel#sidebar_section_title {
    color: #ffffff;
    font-weight: bold;
    font-size: 16px;
    padding: 16px 12px 10px 12px;
    background-color: rgba(102, 126, 234, 0.2);
    border-radius: 10px;
    margin: 8px;
    border-left: 4px solid #667eea;
}

/* أزرار الشريط الجانبي */
QPushButton#sidebar_button {
    background-color: transparent;
    color: #a0aec0;
    border: none;
    text-align: right;
    padding: 16px 20px;
    margin: 4px 10px;
    border-radius: 12px;
    font-size: 15px;
    font-weight: 500;
}

QPushButton#sidebar_button:hover {
    background-color: rgba(102, 126, 234, 0.15);
    color: #e2e8f0;
    border: 1px solid rgba(102, 126, 234, 0.4);
}

QPushButton#sidebar_button:checked {
    background-color: #667eea;
    color: #ffffff;
    font-weight: 600;
    border: 1px solid rgba(102, 126, 234, 0.6);
}

/* العناوين */
QLabel#app_title {
    color: #ffffff;
    font-size: 22px;
    font-weight: bold;
    padding: 8px 16px;
}

QLabel#user_info {
    color: #a0aec0;
    font-size: 14px;
    font-weight: 500;
    background-color: rgba(102, 126, 234, 0.15);
    border: 1px solid rgba(102, 126, 234, 0.3);
    border-radius: 20px;
    padding: 8px 16px;
}

QLabel#window_title {
    color: #e2e8f0;
    font-size: 20px;
    font-weight: 600;
    margin: 16px 0;
}

/* منطقة المحتوى */
QWidget#content_area {
    background-color: #1e2139;
    border: 1px solid #2d3748;
    border-radius: 16px;
    margin: 8px;
}

/* لوحة التحكم */
QWidget#dashboard {
    background-color: #1e2139;
    border-radius: 16px;
    padding: 20px;
}

QLabel#dashboard_header {
    color: #e2e8f0;
    font-size: 24px;
    font-weight: bold;
    padding: 16px 0px;
}

QFrame#dashboard_header_frame {
    background-color: #667eea;
    border: none;
    border-radius: 16px;
    margin: 12px;
    padding: 20px;
}

QLabel#dashboard_main_title {
    color: #ffffff;
    font-size: 22px;
    font-weight: bold;
    padding: 12px;
}

QLabel#section_title {
    color: #e2e8f0;
    font-size: 18px;
    font-weight: 600;
    padding: 18px 0px 12px 0px;
}

/* البطاقات الإحصائية */
QFrame#employees_card, QFrame#stat_card_blue {
    background-color: #4299e1;
    border: 1px solid rgba(66, 153, 225, 0.3);
    border-radius: 16px;
    color: #ffffff;
    padding: 20px;
}

QFrame#salaries_card, QFrame#stat_card_green {
    background-color: #48bb78;
    border: 1px solid rgba(72, 187, 120, 0.3);
    border-radius: 16px;
    color: #ffffff;
    padding: 20px;
}

QFrame#advances_card, QFrame#stat_card_orange {
    background-color: #ed8936;
    border: 1px solid rgba(237, 137, 54, 0.3);
    border-radius: 16px;
    color: #ffffff;
    padding: 20px;
}

QFrame#debts_card, QFrame#stat_card_red {
    background-color: #f56565;
    border: 1px solid rgba(245, 101, 101, 0.3);
    border-radius: 16px;
    color: #ffffff;
    padding: 20px;
}

QFrame#bonuses_card, QFrame#stat_card_purple {
    background-color: #9f7aea;
    border: 1px solid rgba(159, 122, 234, 0.3);
    border-radius: 16px;
    color: #ffffff;
    padding: 20px;
}

QFrame#departments_card, QFrame#stat_card_teal {
    background-color: #4fd1c7;
    border: 1px solid rgba(79, 209, 199, 0.3);
    border-radius: 16px;
    color: #ffffff;
    padding: 20px;
}

/* نصوص البطاقات */
QLabel#card_title, QLabel#stat_card_title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
}

QLabel#card_value, QLabel#stat_card_value {
    color: #ffffff;
    font-size: 32px;
    font-weight: bold;
    margin: 12px 0px;
}

QLabel#card_subtitle, QLabel#stat_card_subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
    font-weight: 500;
    margin-top: 4px;
}

/* الأزرار العامة */
QPushButton {
    background-color: #667eea;
    color: #ffffff;
    border: none;
    border-radius: 10px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    min-width: 100px;
}

QPushButton:hover {
    background-color: #5a67d8;
}

QPushButton:pressed {
    background-color: #4c51bf;
}

QPushButton:disabled {
    background-color: #2d3748;
    color: #718096;
}

/* أزرار الخطر */
QPushButton#danger_button {
    background-color: #f56565;
}

QPushButton#danger_button:hover {
    background-color: #e53e3e;
}

/* أزرار النجاح */
QPushButton#success_button {
    background-color: #48bb78;
}

QPushButton#success_button:hover {
    background-color: #38a169;
}

/* أزرار التحذير */
QPushButton#warning_button {
    background-color: #ed8936;
}

QPushButton#warning_button:hover {
    background-color: #dd6b20;
}

/* أزرار المعلومات */
QPushButton#info_button {
    background-color: #4299e1;
}

QPushButton#info_button:hover {
    background-color: #3182ce;
}

/* أزرار الإجراءات السريعة */
QPushButton#quick_action_button {
    background-color: #667eea;
    color: #ffffff;
    border: none;
    border-radius: 12px;
    padding: 14px 24px;
    font-size: 14px;
    font-weight: 600;
}

QPushButton#quick_action_button:hover {
    background-color: #5a67d8;
}

QPushButton#dashboard_action_button {
    font-size: 16px;
    font-weight: bold;
    color: #ffffff;
    border: none;
    border-radius: 12px;
    padding: 16px 24px;
    background-color: #667eea;
    min-width: 140px;
}

QPushButton#dashboard_action_button:hover {
    background-color: #5a67d8;
}

/* حقول الإدخال */
QLineEdit {
    background-color: #252545;
    border: 2px solid #2d3748;
    border-radius: 10px;
    padding: 12px 16px;
    font-size: 14px;
    color: #e2e8f0;
    font-weight: 500;
}

QLineEdit:focus {
    border-color: #667eea;
    background-color: #1e2139;
}

QLineEdit:hover {
    border-color: #667eea;
    background-color: #1e2139;
}

/* القوائم المنسدلة */
QComboBox {
    background-color: #252545;
    border: 2px solid #2d3748;
    border-radius: 10px;
    padding: 12px 16px;
    font-size: 14px;
    color: #e2e8f0;
    font-weight: 500;
    min-width: 120px;
}

QComboBox:focus {
    border-color: #667eea;
    background-color: #1e2139;
}

QComboBox:hover {
    border-color: #667eea;
    background-color: #1e2139;
}

QComboBox::drop-down {
    border: none;
    width: 30px;
    background: transparent;
}

QComboBox::down-arrow {
    width: 16px;
    height: 16px;
    background-color: #667eea;
    border-radius: 3px;
}

QComboBox QAbstractItemView {
    background-color: #252545;
    color: #e2e8f0;
    selection-background-color: #667eea;
    selection-color: #ffffff;
    border: 2px solid #2d3748;
    border-radius: 10px;
    padding: 8px;
}

QComboBox QAbstractItemView::item {
    padding: 10px 16px;
    border-radius: 6px;
    margin: 2px;
}

QComboBox QAbstractItemView::item:hover {
    background-color: rgba(102, 126, 234, 0.2);
    color: #90cdf4;
}

/* حقول التاريخ */
QDateEdit {
    background-color: #252545;
    border: 2px solid #2d3748;
    border-radius: 10px;
    padding: 12px 16px;
    font-size: 14px;
    color: #e2e8f0;
    font-weight: 500;
    min-width: 140px;
}

QDateEdit:focus {
    border-color: #667eea;
    background-color: #1e2139;
}

QDateEdit:hover {
    border-color: #667eea;
    background-color: #1e2139;
}

/* مربعات النص */
QTextEdit {
    background-color: #252545;
    border: 2px solid #2d3748;
    border-radius: 10px;
    padding: 12px 16px;
    font-size: 14px;
    color: #e2e8f0;
    font-weight: 500;
}

QTextEdit:focus {
    border-color: #667eea;
    background-color: #1e2139;
}

QTextEdit:hover {
    border-color: #667eea;
    background-color: #1e2139;
}

/* حقول الأرقام */
QSpinBox, QDoubleSpinBox {
    background-color: #252545;
    border: 2px solid #2d3748;
    border-radius: 10px;
    padding: 12px 16px;
    font-size: 14px;
    color: #e2e8f0;
    font-weight: 500;
    min-width: 100px;
}

QSpinBox:focus, QDoubleSpinBox:focus {
    border-color: #667eea;
    background-color: #1e2139;
}

/* الجداول */
QTableWidget {
    background-color: #1e2139;
    alternate-background-color: #252545;
    selection-background-color: #667eea;
    selection-color: #ffffff;
    border: 2px solid #2d3748;
    gridline-color: #4a5568;
    color: #e2e8f0;
    border-radius: 12px;
    font-size: 13px;
}

QTableWidget::item {
    padding: 12px 16px;
    border-bottom: 1px solid #2d3748;
    color: #e2e8f0;
    border-radius: 4px;
    margin: 1px;
}

QTableWidget::item:hover {
    background-color: rgba(102, 126, 234, 0.1);
    color: #90cdf4;
}

QTableWidget::item:selected {
    background-color: #667eea;
    color: #ffffff;
    border-radius: 6px;
}

QHeaderView::section {
    background-color: #16213e;
    color: #e2e8f0;
    padding: 14px 16px;
    border: none;
    font-weight: 600;
    font-size: 14px;
    border-bottom: 2px solid #667eea;
    border-radius: 8px 8px 0px 0px;
}

QHeaderView::section:hover {
    background-color: rgba(102, 126, 234, 0.2);
}

/* شريط التمرير */
QScrollBar:vertical {
    background-color: #1e2139;
    width: 14px;
    border-radius: 7px;
    margin: 0px;
    border: 1px solid #2d3748;
}

QScrollBar::handle:vertical {
    background-color: #667eea;
    border-radius: 6px;
    min-height: 30px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #5a67d8;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    border: none;
    background: none;
    height: 0px;
}

QScrollBar:horizontal {
    background-color: #1e2139;
    height: 14px;
    border-radius: 7px;
    margin: 0px;
    border: 1px solid #2d3748;
}

QScrollBar::handle:horizontal {
    background-color: #667eea;
    border-radius: 6px;
    min-width: 30px;
    margin: 2px;
}

/* مجموعات العناصر */
QGroupBox {
    font-weight: 600;
    border: 2px solid #2d3748;
    border-radius: 12px;
    margin-top: 16px;
    color: #e2e8f0;
    background-color: #252545;
    padding: 16px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 16px;
    padding: 4px 12px;
    color: #90cdf4;
    background-color: #1e2139;
    border: 1px solid #667eea;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
}

/* التسميات */
QLabel {
    color: #e2e8f0;
    font-weight: 500;
}

QLabel#section_header {
    color: #90cdf4;
    font-size: 18px;
    font-weight: bold;
    margin: 16px 0px 8px 0px;
}

QLabel#info_label {
    color: #a0aec0;
    font-size: 13px;
    font-weight: 400;
}

/* شريط التقدم */
QProgressBar {
    border: 2px solid #2d3748;
    border-radius: 12px;
    text-align: center;
    color: #e2e8f0;
    background-color: #252545;
    font-weight: 600;
    font-size: 13px;
    padding: 2px;
}

QProgressBar::chunk {
    background-color: #667eea;
    border-radius: 10px;
}

/* خانات الاختيار */
QCheckBox {
    color: #e2e8f0;
    font-weight: 500;
    spacing: 8px;
}

QCheckBox::indicator {
    width: 20px;
    height: 20px;
}

QCheckBox::indicator:unchecked {
    border: 2px solid #2d3748;
    background-color: #252545;
    border-radius: 6px;
}

QCheckBox::indicator:unchecked:hover {
    border-color: #667eea;
    background-color: #1e2139;
}

QCheckBox::indicator:checked {
    border: 2px solid #667eea;
    background-color: #667eea;
    border-radius: 6px;
}

QCheckBox::indicator:checked:hover {
    background-color: #5a67d8;
}

/* أزرار الراديو */
QRadioButton {
    color: #e2e8f0;
    font-weight: 500;
    spacing: 8px;
}

QRadioButton::indicator {
    width: 20px;
    height: 20px;
}

QRadioButton::indicator:unchecked {
    border: 2px solid #2d3748;
    background-color: #252545;
    border-radius: 10px;
}

QRadioButton::indicator:unchecked:hover {
    border-color: #667eea;
    background-color: #1e2139;
}

QRadioButton::indicator:checked {
    border: 2px solid #667eea;
    background-color: #667eea;
    border-radius: 10px;
}

QRadioButton::indicator:checked:hover {
    background-color: #5a67d8;
}

/* التبويبات */
QTabWidget::pane {
    border: 2px solid #2d3748;
    background-color: #1e2139;
    border-radius: 12px;
    padding: 16px;
}

QTabBar::tab {
    background-color: #252545;
    color: #a0aec0;
    padding: 12px 20px;
    margin-right: 4px;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    font-weight: 500;
    font-size: 14px;
}

QTabBar::tab:selected {
    background-color: #667eea;
    color: #ffffff;
    font-weight: 600;
}

QTabBar::tab:hover:!selected {
    background-color: #1e2139;
    color: #e2e8f0;
}

/* شريط الحالة */
QStatusBar {
    background-color: #16213e;
    color: #a0aec0;
    border-top: 2px solid #2d3748;
    padding: 8px 16px;
    font-weight: 500;
    font-size: 13px;
}

QStatusBar::item {
    border: none;
    padding: 4px 8px;
    border-radius: 6px;
    background-color: rgba(102, 126, 234, 0.1);
    margin: 2px;
}

/* القوائم */
QMenuBar {
    background-color: #16213e;
    color: #e2e8f0;
    border-bottom: 2px solid #2d3748;
    padding: 4px;
}

QMenuBar::item {
    background-color: transparent;
    padding: 8px 16px;
    border-radius: 8px;
    font-weight: 500;
}

QMenuBar::item:selected {
    background-color: #667eea;
    color: #ffffff;
}

QMenu {
    background-color: #252545;
    color: #e2e8f0;
    border: 2px solid #2d3748;
    border-radius: 12px;
    padding: 8px;
}

QMenu::item {
    padding: 10px 20px;
    border-radius: 8px;
    margin: 2px;
    font-weight: 500;
}

QMenu::item:selected {
    background-color: #667eea;
    color: #ffffff;
}

QMenu::separator {
    height: 2px;
    background-color: #2d3748;
    margin: 8px 16px;
    border-radius: 1px;
}

/* النوافذ المنبثقة */
QDialog {
    background-color: #1a1a2e;
    border: 2px solid #2d3748;
    border-radius: 16px;
}

QDialog QLabel {
    color: #e2e8f0;
    font-weight: 500;
}

/* أشرطة الأدوات */
QToolBar {
    background-color: #16213e;
    border: 1px solid #2d3748;
    border-radius: 8px;
    padding: 4px;
    spacing: 4px;
}

QToolButton {
    background-color: transparent;
    color: #a0aec0;
    border: none;
    border-radius: 8px;
    padding: 8px;
    font-weight: 500;
}

QToolButton:hover {
    background-color: rgba(102, 126, 234, 0.2);
    color: #e2e8f0;
}

QToolButton:pressed {
    background-color: #667eea;
    color: #ffffff;
}

/* عناصر القائمة */
QListWidget {
    background-color: #1e2139;
    border: 2px solid #2d3748;
    border-radius: 12px;
    color: #e2e8f0;
    font-weight: 500;
    padding: 8px;
}

QListWidget::item {
    padding: 12px 16px;
    border-radius: 8px;
    margin: 2px;
}

QListWidget::item:hover {
    background-color: rgba(102, 126, 234, 0.15);
    color: #90cdf4;
}

QListWidget::item:selected {
    background-color: #667eea;
    color: #ffffff;
    font-weight: 600;
}

/* عرض الشجرة */
QTreeWidget {
    background-color: #1e2139;
    border: 2px solid #2d3748;
    border-radius: 12px;
    color: #e2e8f0;
    font-weight: 500;
    padding: 8px;
}

QTreeWidget::item {
    padding: 8px 12px;
    border-radius: 6px;
    margin: 1px;
}

QTreeWidget::item:hover {
    background-color: rgba(102, 126, 234, 0.15);
    color: #90cdf4;
}

QTreeWidget::item:selected {
    background-color: #667eea;
    color: #ffffff;
    font-weight: 600;
}

/* شرائح التمرير */
QSlider::groove:horizontal {
    background-color: #252545;
    height: 8px;
    border-radius: 4px;
    border: 1px solid #2d3748;
}

QSlider::handle:horizontal {
    background-color: #667eea;
    width: 20px;
    height: 20px;
    border-radius: 10px;
    margin: -6px 0;
}

QSlider::handle:horizontal:hover {
    background-color: #5a67d8;
}

/* تحسينات خاصة للنوافذ الفرعية */
QMdiSubWindow {
    background-color: #1e2139;
    border: 2px solid #2d3748;
    border-radius: 12px;
}

QMdiSubWindow::title {
    background-color: #667eea;
    color: #ffffff;
    font-weight: 600;
    padding: 8px 16px;
    border-radius: 10px 10px 0px 0px;
}

/* تحسينات للرسائل والتنبيهات */
QMessageBox {
    background-color: #1e2139;
    border: 2px solid #2d3748;
    border-radius: 16px;
}

QMessageBox QLabel {
    color: #e2e8f0;
    font-weight: 500;
    font-size: 14px;
}

QMessageBox QPushButton {
    min-width: 80px;
    margin: 4px;
}

/* تنسيقات خاصة لنافذة إضافة الموظف */
QDialog#EmployeeForm {
    background-color: #f5f5f5;
    border: 2px solid #667eea;
    border-radius: 16px;
}

QDialog#EmployeeForm QFrame {
    background-color: #1e2139;
    border: 1px solid #2d3748;
    border-radius: 12px;
}

QDialog#EmployeeForm QGroupBox {
    background-color: #252545;
    border: 2px solid #667eea;
    border-radius: 12px;
    color: #e2e8f0;
    font-weight: 600;
    font-size: 16px;
    margin-top: 16px;
    padding: 20px;
}

QDialog#EmployeeForm QGroupBox::title {
    subcontrol-origin: margin;
    left: 20px;
    padding: 8px 15px;
    background-color: #667eea;
    color: #ffffff;
    border-radius: 8px;
    font-size: 15px;
    font-weight: bold;
}

QDialog#EmployeeForm QLineEdit {
    background-color: #ffffff;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 14px;
    color: #2d3436;
    font-weight: 500;
    min-height: 25px;
    min-width: 250px;
}

QDialog#EmployeeForm QLineEdit:focus {
    border-color: #3498db;
    background-color: #f8f9ff;
    outline: none;
}

QDialog#EmployeeForm QLineEdit:hover {
    border-color: #74b9ff;
    background-color: #fdfdff;
}

QDialog#EmployeeForm QDateEdit {
    background-color: #ffffff;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 14px;
    color: #2d3436;
    font-weight: 500;
    min-height: 25px;
    min-width: 250px;
}

QDialog#EmployeeForm QDateEdit:focus {
    border-color: #3498db;
    background-color: #f8f9ff;
    outline: none;
}

QDialog#EmployeeForm QDateEdit:hover {
    border-color: #74b9ff;
    background-color: #fdfdff;
}

QDialog#EmployeeForm QComboBox {
    background-color: #ffffff;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 14px;
    color: #2d3436;
    font-weight: 500;
    min-height: 25px;
    min-width: 250px;
}

QDialog#EmployeeForm QComboBox:focus {
    border-color: #27ae60;
    background-color: #f0fff4;
    outline: none;
}

QDialog#EmployeeForm QComboBox:hover {
    border-color: #55a3ff;
    background-color: #fdfdff;
}

QDialog#EmployeeForm QComboBox::drop-down {
    border: none;
    width: 35px;
    background-color: transparent;
}

QDialog#EmployeeForm QComboBox::down-arrow {
    image: none;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid #27ae60;
    margin-right: 12px;
}

QDialog#EmployeeForm QComboBox QAbstractItemView {
    background-color: #ffffff;
    color: #2d3436;
    selection-background-color: #27ae60;
    selection-color: #ffffff;
    border: 2px solid #e9ecef;
    border-radius: 8px;
}

QDialog#EmployeeForm QTextEdit {
    background-color: #ffffff;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 14px;
    color: #2d3436;
    font-weight: 500;
}

QDialog#EmployeeForm QTextEdit:focus {
    border-color: #3498db;
    background-color: #f8f9ff;
    outline: none;
}

QDialog#EmployeeForm QTextEdit:hover {
    border-color: #74b9ff;
    background-color: #fdfdff;
}

QDialog#EmployeeForm QLabel {
    color: #2d3436;
    font-size: 14px;
    font-weight: 600;
    padding: 8px 5px;
    min-width: 120px;
}

QDialog#EmployeeForm QPushButton {
    background-color: #667eea;
    color: #ffffff;
    border: none;
    border-radius: 10px;
    padding: 15px 25px;
    font-size: 16px;
    font-weight: bold;
    min-width: 160px;
    min-height: 50px;
}

QDialog#EmployeeForm QPushButton:hover {
    background-color: #5a67d8;
}

QDialog#EmployeeForm QPushButton:pressed {
    background-color: #4c51bf;
}

/* تنسيقات خاصة لمجموعة الرقم الوظيفي */
QDialog#EmployeeForm QGroupBox[title*="الرقم الوظيفي"] {
    background-color: white;
    border: 2px solid #e74c3c;
    border-radius: 12px;
    color: #2c3e50;
    font-weight: bold;
    font-size: 16px;
    margin-top: 10px;
    padding-top: 20px;
}

QDialog#EmployeeForm QGroupBox[title*="الرقم الوظيفي"]::title {
    subcontrol-origin: margin;
    left: 20px;
    padding: 8px 15px;
    background-color: #e74c3c;
    color: white;
    border-radius: 8px;
    font-size: 15px;
    font-weight: bold;
}

/* تنسيق عرض الرقم الوظيفي */
QDialog#EmployeeForm QGroupBox[title*="الرقم الوظيفي"] QLabel {
    color: #2d3436;
    font-size: 14px;
    font-weight: 600;
    padding: 8px 5px;
    text-align: center;
}

/* تنسيق خاص لعرض الرقم */
QDialog#EmployeeForm QGroupBox[title*="الرقم الوظيفي"] QLabel[text*="EMP"] {
    color: #e74c3c;
    font-size: 18px;
    font-weight: bold;
    background-color: #fdf2f2;
    border: 2px solid #e74c3c;
    border-radius: 8px;
    padding: 15px 10px;
    margin: 5px;
    text-align: center;
}

/* تنسيقات خاصة لمجموعة المعلومات الوظيفية */
QDialog#EmployeeForm QGroupBox[title*="المعلومات الوظيفية"] {
    background-color: white;
    border: 2px solid #27ae60;
    border-radius: 12px;
    color: #2c3e50;
    font-weight: bold;
    font-size: 16px;
    margin-top: 10px;
    padding-top: 20px;
}

QDialog#EmployeeForm QGroupBox[title*="المعلومات الوظيفية"]::title {
    subcontrol-origin: margin;
    left: 20px;
    padding: 8px 15px;
    background-color: #27ae60;
    color: white;
    border-radius: 8px;
    font-size: 15px;
    font-weight: bold;
}

/* تنسيقات خاصة لمجموعة المعلومات الشخصية */
QDialog#EmployeeForm QGroupBox[title*="المعلومات الشخصية"] {
    background-color: white;
    border: 2px solid #3498db;
    border-radius: 12px;
    color: #2c3e50;
    font-weight: bold;
    font-size: 16px;
    margin-top: 10px;
    padding-top: 20px;
}

QDialog#EmployeeForm QGroupBox[title*="المعلومات الشخصية"]::title {
    subcontrol-origin: margin;
    left: 20px;
    padding: 8px 15px;
    background-color: #3498db;
    color: white;
    border-radius: 8px;
    font-size: 15px;
    font-weight: bold;
}

/* تنسيقات خاصة للأزرار في نافذة الموظف */
QDialog#EmployeeForm QPushButton[text*="حفظ"] {
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: 10px;
    padding: 15px 25px;
    font-size: 16px;
    font-weight: bold;
    min-width: 160px;
    min-height: 50px;
}

QDialog#EmployeeForm QPushButton[text*="حفظ"]:hover {
    background-color: #218838;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

QDialog#EmployeeForm QPushButton[text*="حفظ"]:pressed {
    background-color: #1e7e34;
    transform: translateY(0px);
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

QDialog#EmployeeForm QPushButton[text*="مسح"] {
    background-color: #ffc107;
    color: white;
    border: none;
    border-radius: 10px;
    padding: 15px 25px;
    font-size: 16px;
    font-weight: bold;
    min-width: 140px;
    min-height: 50px;
}

QDialog#EmployeeForm QPushButton[text*="مسح"]:hover {
    background-color: #e0a800;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(255, 193, 7, 0.3);
}

QDialog#EmployeeForm QPushButton[text*="مسح"]:pressed {
    background-color: #d39e00;
    transform: translateY(0px);
    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
}

QDialog#EmployeeForm QPushButton[text*="إلغاء"] {
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 10px;
    padding: 15px 25px;
    font-size: 16px;
    font-weight: bold;
    min-width: 130px;
    min-height: 50px;
}

QDialog#EmployeeForm QPushButton[text*="إلغاء"]:hover {
    background-color: #c82333;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

QDialog#EmployeeForm QPushButton[text*="إلغاء"]:pressed {
    background-color: #bd2130;
    transform: translateY(0px);
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

/* تنسيق إطار الأزرار */
QDialog#EmployeeForm QFrame {
    background-color: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    margin: 15px 0px;
}

/* تنسيقات الرأس في نافذة الموظف */
QDialog#EmployeeForm QFrame[objectName*="header"] {
    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                stop: 0 #667eea, stop: 1 #764ba2);
    border: none;
    border-radius: 16px;
    margin: 12px;
    padding: 20px;
}

QDialog#EmployeeForm QLabel[objectName*="title"] {
    color: #ffffff;
    font-size: 22px;
    font-weight: bold;
    padding: 8px;
    margin: 0px;
    background-color: transparent;
}

QDialog#EmployeeForm QLabel[objectName*="subtitle"] {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: 600;
    background-color: #f3e5f5;
    padding: 6px 15px;
    border-radius: 6px;
    border-left: 3px solid #9c27b0;
    margin: 2px;
}

/* تحسين أحجام الخطوط في النافذة */
QDialog#EmployeeForm {
    font-size: 14px;
}

QDialog#EmployeeForm QLabel {
    font-size: 14px;
    font-weight: 600;
    color: #2d3436;
    padding: 8px 5px;
    min-width: 120px;
}

QDialog#EmployeeForm QLineEdit {
    font-size: 16px;
    padding: 12px 15px;
    min-height: 25px;
    min-width: 500px;
}

QDialog#EmployeeForm QDateEdit {
    font-size: 14px;
    padding: 12px 15px;
    min-height: 25px;
    min-width: 250px;
}

QDialog#EmployeeForm QComboBox {
    font-size: 14px;
    padding: 12px 15px;
    min-height: 25px;
    min-width: 250px;
}

QDialog#EmployeeForm QTextEdit {
    font-size: 14px;
    padding: 12px 15px;
}

/* تنسيق خاص لحقل الاسم الكامل المكبر */
QDialog#EmployeeForm QLineEdit[placeholderText*="الاسم الثلاثي"] {
    font-size: 16px;
    min-width: 500px;
    font-weight: 500;
}
