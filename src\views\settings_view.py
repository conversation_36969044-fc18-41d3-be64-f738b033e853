"""
واجهة إعدادات التطبيق
Application Settings View
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QComboBox,
    QPushButton, QLabel, QFrame, QCheckBox, QSpinBox,
    QTabWidget, QFileDialog, QSlider
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from ..utils import (
    apply_rtl_layout, show_message, show_confirmation, setup_combobox,
    get_combobox_value, set_combobox_value
)
from ..utils.theme_manager import theme_manager



class SettingsView(QWidget):
    """واجهة إعدادات التطبيق"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setObjectName("settings_view")
        apply_rtl_layout(self)
        
        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # إنشاء رأس الصفحة
        self.create_header(main_layout)
        
        # إنشاء التبويبات
        self.create_tabs(main_layout)
        
        # إنشاء أزرار الإجراءات
        self.create_action_buttons(main_layout)
        
    def create_header(self, layout: QVBoxLayout):
        """إنشاء رأس الصفحة المحسن"""
        # إنشاء إطار العنوان الرئيسي المدمج
        header_frame = QFrame()
        header_frame.setObjectName("compact_header_frame")
        header_frame.setFixedHeight(120)
        header_frame.setStyleSheet("""
            QFrame#compact_header_frame {
                background-color: #d5f4e6;
                border: 2px solid #27ae60;
                border-radius: 10px;
                margin: 5px;
            }
        """)

        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(15, 8, 15, 8)
        header_layout.setSpacing(5)

        # العنوان الرئيسي
        self.main_title = QLabel("⚙️ إعدادات التطبيق")
        self.main_title.setAlignment(Qt.AlignCenter)
        self.main_title.setFixedHeight(60)
        self.main_title.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 22px;
                font-weight: bold;
                color: #2c3e50;
                background-color: transparent;
                padding: 8px;
                margin: 0px;
            }
        """)

        # العنوان الفرعي
        self.subtitle = QLabel("🔧 تخصيص وإعداد النظام حسب احتياجاتك")
        self.subtitle.setAlignment(Qt.AlignCenter)
        self.subtitle.setFixedHeight(40)
        self.subtitle.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 12px;
                font-weight: 600;
                color: #34495e;
                background-color: #e8f8f5;
                padding: 6px 15px;
                border-radius: 6px;
                border-left: 3px solid #27ae60;
                margin: 2px;
            }
        """)

        # تجميع العناصر
        header_layout.addWidget(self.main_title)
        header_layout.addWidget(self.subtitle)

        layout.addWidget(header_frame)
        
    def create_tabs(self, layout: QVBoxLayout):
        """إنشاء التبويبات المحسنة"""
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #dee2e6;
                border-radius: 8px;
                background-color: #ffffff;
                margin-top: 5px;
            }

            QTabBar::tab {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-bottom: none;
                border-radius: 8px 8px 0px 0px;
                padding: 12px 20px;
                margin-right: 2px;
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                font-weight: 600;
                color: #495057;
                min-width: 120px;
            }

            QTabBar::tab:selected {
                background-color: #ffffff;
                border-bottom: 2px solid #ffffff;
                color: #27ae60;
                font-weight: bold;
            }

            QTabBar::tab:hover:!selected {
                background-color: #e9ecef;
                color: #27ae60;
            }
        """)

        # تبويب المظهر المحسن
        self.appearance_tab = self.create_appearance_tab()
        self.tab_widget.addTab(self.appearance_tab, "🎨 المظهر")

        # تبويب الإعدادات العامة المحسن
        self.general_tab = self.create_general_tab()
        self.tab_widget.addTab(self.general_tab, "⚙️ عام")

        # تبويب النظام المحسن
        self.system_tab = self.create_system_tab()
        self.tab_widget.addTab(self.system_tab, "🖥️ النظام")

        layout.addWidget(self.tab_widget)

    def create_appearance_tab(self):
        """إنشاء تبويب المظهر المحسن"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # قسم الثيم
        theme_frame = self.create_settings_frame("🎨 الثيم والمظهر")
        theme_layout = QVBoxLayout()

        # اختيار الثيم
        theme_row = QHBoxLayout()
        theme_label = QLabel("الثيم:")
        theme_label.setStyleSheet("font-size: 14px; font-weight: 600; color: #495057; min-width: 100px;")

        self.theme_combo = QComboBox()
        themes = theme_manager.get_available_themes()
        theme_items = [(display_name, theme_id) for theme_id, display_name in themes.items()]
        setup_combobox(self.theme_combo, theme_items)
        self.theme_combo.setStyleSheet("""
            QComboBox {
                font-size: 14px; padding: 8px 12px; border: 2px solid #dee2e6;
                border-radius: 6px; background-color: #ffffff; min-width: 150px;
            }
            QComboBox:focus { border-color: #27ae60; }
        """)

        toggle_btn = QPushButton("🔄 تبديل سريع")
        toggle_btn.setStyleSheet("""
            QPushButton {
                font-size: 14px; font-weight: bold; color: white; background-color: #6c757d;
                border: none; border-radius: 6px; padding: 8px 15px; min-width: 100px;
            }
            QPushButton:hover { background-color: #5a6268; }
        """)

        theme_row.addWidget(theme_label)
        theme_row.addWidget(self.theme_combo)
        theme_row.addWidget(toggle_btn)
        theme_row.addStretch()

        # حجم الخط
        font_row = QHBoxLayout()
        font_label = QLabel("حجم الخط:")
        font_label.setStyleSheet("font-size: 14px; font-weight: 600; color: #495057; min-width: 100px;")

        self.font_size_slider = QSlider(Qt.Horizontal)
        self.font_size_slider.setRange(8, 72)
        self.font_size_slider.setValue(12)
        self.font_size_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                border: 1px solid #dee2e6; height: 8px; background: #f8f9fa; border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: #27ae60; border: 2px solid #27ae60; width: 18px; margin: -5px 0;
                border-radius: 9px;
            }
        """)

        self.font_size_label = QLabel("12")
        self.font_size_label.setStyleSheet("""
            QLabel {
                font-size: 14px; font-weight: bold; color: #27ae60;
                background-color: #d5f4e6; padding: 6px 10px; border-radius: 4px;
                min-width: 30px; text-align: center;
            }
        """)

        font_row.addWidget(font_label)
        font_row.addWidget(self.font_size_slider)
        font_row.addWidget(self.font_size_label)
        font_row.addStretch()

        theme_layout.addLayout(theme_row)
        theme_layout.addLayout(font_row)
        theme_frame.layout().addLayout(theme_layout)

        layout.addWidget(theme_frame)
        layout.addStretch()

        # ربط الإشارات
        self.theme_combo.currentTextChanged.connect(self.on_theme_changed)
        toggle_btn.clicked.connect(self.toggle_theme)
        self.font_size_slider.valueChanged.connect(self.on_font_size_changed)

        # تحميل الإعدادات
        current_theme = theme_manager.get_current_theme()
        set_combobox_value(self.theme_combo, current_theme)

        return tab

    def create_general_tab(self):
        """إنشاء تبويب الإعدادات العامة المحسن"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # قسم التطبيق
        app_frame = self.create_settings_frame("📱 إعدادات التطبيق")
        app_layout = QVBoxLayout()

        self.startup_checkbox = self.create_checkbox("🚀 تشغيل مع بدء النظام")
        self.minimize_to_tray_checkbox = self.create_checkbox("📥 تصغير إلى شريط المهام")
        self.auto_save_checkbox = self.create_checkbox("💾 الحفظ التلقائي")

        # فترة الحفظ التلقائي
        save_row = QHBoxLayout()
        save_label = QLabel("فترة الحفظ:")
        save_label.setStyleSheet("font-size: 14px; font-weight: 600; color: #495057; min-width: 100px;")

        self.auto_save_interval_spin = QSpinBox()
        self.auto_save_interval_spin.setRange(1, 60)
        self.auto_save_interval_spin.setValue(5)
        self.auto_save_interval_spin.setSuffix(" دقائق")
        self.auto_save_interval_spin.setStyleSheet("""
            QSpinBox {
                font-size: 14px; padding: 8px 12px; border: 2px solid #dee2e6;
                border-radius: 6px; background-color: #ffffff; min-width: 100px;
            }
            QSpinBox:focus { border-color: #27ae60; }
        """)

        save_row.addWidget(save_label)
        save_row.addWidget(self.auto_save_interval_spin)
        save_row.addStretch()

        app_layout.addWidget(self.startup_checkbox)
        app_layout.addWidget(self.minimize_to_tray_checkbox)
        app_layout.addWidget(self.auto_save_checkbox)
        app_layout.addLayout(save_row)
        app_frame.layout().addLayout(app_layout)

        # قسم الأمان
        security_frame = self.create_settings_frame("🔒 الأمان والخصوصية")
        security_layout = QVBoxLayout()

        # انتهاء الجلسة
        session_row = QHBoxLayout()
        session_label = QLabel("انتهاء الجلسة:")
        session_label.setStyleSheet("font-size: 14px; font-weight: 600; color: #495057; min-width: 120px;")

        self.session_timeout_spin = QSpinBox()
        self.session_timeout_spin.setRange(5, 480)
        self.session_timeout_spin.setValue(60)
        self.session_timeout_spin.setSuffix(" دقيقة")
        self.session_timeout_spin.setStyleSheet("""
            QSpinBox {
                font-size: 14px; padding: 8px 12px; border: 2px solid #dee2e6;
                border-radius: 6px; background-color: #ffffff; min-width: 120px;
            }
            QSpinBox:focus { border-color: #27ae60; }
        """)

        session_row.addWidget(session_label)
        session_row.addWidget(self.session_timeout_spin)
        session_row.addStretch()

        self.log_activities_checkbox = self.create_checkbox("📝 تسجيل الأنشطة", True)

        security_layout.addLayout(session_row)
        security_layout.addWidget(self.log_activities_checkbox)
        security_frame.layout().addLayout(security_layout)

        # قسم الإشعارات
        notifications_frame = self.create_settings_frame("🔔 الإشعارات")
        notifications_layout = QVBoxLayout()

        self.show_notifications_checkbox = self.create_checkbox("📢 إظهار الإشعارات", True)
        self.sound_notifications_checkbox = self.create_checkbox("🔊 الإشعارات الصوتية")

        notifications_layout.addWidget(self.show_notifications_checkbox)
        notifications_layout.addWidget(self.sound_notifications_checkbox)
        notifications_frame.layout().addLayout(notifications_layout)

        layout.addWidget(app_frame)
        layout.addWidget(security_frame)
        layout.addWidget(notifications_frame)
        layout.addStretch()

        return tab

    def create_system_tab(self):
        """إنشاء تبويب النظام المحسن"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # قسم اللغة والمنطقة
        language_frame = self.create_settings_frame("🌐 اللغة والمنطقة")
        language_layout = QVBoxLayout()

        # اختيار اللغة
        lang_row = QHBoxLayout()
        lang_label = QLabel("اللغة:")
        lang_label.setStyleSheet("font-size: 14px; font-weight: 600; color: #495057; min-width: 100px;")

        self.language_combo = QComboBox()
        language_items = [("العربية", "ar"), ("English", "en")]
        setup_combobox(self.language_combo, language_items, "ar")
        self.language_combo.setStyleSheet("""
            QComboBox {
                font-size: 14px; padding: 8px 12px; border: 2px solid #dee2e6;
                border-radius: 6px; background-color: #ffffff; min-width: 150px;
            }
            QComboBox:focus { border-color: #27ae60; }
        """)

        lang_row.addWidget(lang_label)
        lang_row.addWidget(self.language_combo)
        lang_row.addStretch()

        # تنسيق التاريخ
        date_row = QHBoxLayout()
        date_label = QLabel("تنسيق التاريخ:")
        date_label.setStyleSheet("font-size: 14px; font-weight: 600; color: #495057; min-width: 100px;")

        self.date_format_combo = QComboBox()
        date_formats = [("YYYY-MM-DD", "yyyy-MM-dd"), ("DD/MM/YYYY", "dd/MM/yyyy"), ("MM/DD/YYYY", "MM/dd/yyyy")]
        setup_combobox(self.date_format_combo, date_formats)
        self.date_format_combo.setStyleSheet("""
            QComboBox {
                font-size: 14px; padding: 8px 12px; border: 2px solid #dee2e6;
                border-radius: 6px; background-color: #ffffff; min-width: 150px;
            }
            QComboBox:focus { border-color: #27ae60; }
        """)

        date_row.addWidget(date_label)
        date_row.addWidget(self.date_format_combo)
        date_row.addStretch()

        language_layout.addLayout(lang_row)
        language_layout.addLayout(date_row)
        language_frame.layout().addLayout(language_layout)

        # قسم الأداء
        performance_frame = self.create_settings_frame("⚡ الأداء والذاكرة")
        performance_layout = QVBoxLayout()

        self.cache_enabled_checkbox = self.create_checkbox("🗄️ تفعيل التخزين المؤقت", True)
        self.preload_data_checkbox = self.create_checkbox("📊 تحميل البيانات مسبقاً")

        # حجم الذاكرة المؤقتة
        cache_row = QHBoxLayout()
        cache_label = QLabel("حجم الذاكرة المؤقتة:")
        cache_label.setStyleSheet("font-size: 14px; font-weight: 600; color: #495057; min-width: 150px;")

        self.cache_size_spin = QSpinBox()
        self.cache_size_spin.setRange(10, 1000)
        self.cache_size_spin.setValue(100)
        self.cache_size_spin.setSuffix(" MB")
        self.cache_size_spin.setStyleSheet("""
            QSpinBox {
                font-size: 14px; padding: 8px 12px; border: 2px solid #dee2e6;
                border-radius: 6px; background-color: #ffffff; min-width: 100px;
            }
            QSpinBox:focus { border-color: #27ae60; }
        """)

        cache_row.addWidget(cache_label)
        cache_row.addWidget(self.cache_size_spin)
        cache_row.addStretch()

        performance_layout.addWidget(self.cache_enabled_checkbox)
        performance_layout.addWidget(self.preload_data_checkbox)
        performance_layout.addLayout(cache_row)
        performance_frame.layout().addLayout(performance_layout)

        layout.addWidget(language_frame)
        layout.addWidget(performance_frame)
        layout.addStretch()

        return tab

    def create_settings_frame(self, title):
        """إنشاء إطار إعدادات موحد"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 10px;
                padding: 15px;
                margin: 5px 0px;
            }
        """)

        layout = QVBoxLayout(frame)
        layout.setSpacing(15)

        # عنوان القسم
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                padding: 8px 0px;
                border-bottom: 2px solid #27ae60;
                margin-bottom: 10px;
            }
        """)

        layout.addWidget(title_label)
        return frame

    def create_checkbox(self, text, checked=False):
        """إنشاء خانة اختيار موحدة"""
        checkbox = QCheckBox(text)
        checkbox.setChecked(checked)
        checkbox.setStyleSheet("""
            QCheckBox {
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 14px;
                font-weight: 600;
                color: #2c3e50;
                spacing: 8px;
                padding: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #27ae60;
                border-radius: 4px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #27ae60;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDNMNC41IDguNUwyIDYiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
            }
            QCheckBox::indicator:hover {
                border-color: #1e8449;
            }
        """)
        return checkbox

    def create_action_buttons(self, layout: QVBoxLayout):
        """إنشاء أزرار الإجراءات المحسنة"""
        buttons_frame = QFrame()
        buttons_frame.setObjectName("buttons_frame")
        buttons_frame.setStyleSheet("""
            QFrame#buttons_frame {
                background-color: #ffffff;
                border: 1px solid #e9ecef;
                border-radius: 6px;
                padding: 15px;
                margin: 10px 0px;
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(12)

        # زر حفظ جميع الإعدادات
        self.save_all_btn = QPushButton("💾 حفظ جميع الإعدادات")
        self.save_all_btn.setStyleSheet("""
            QPushButton {
                font-size: 14px;
                font-weight: bold;
                color: white;
                background-color: #28a745;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                min-width: 180px;
                min-height: 45px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
        """)

        # زر إعادة تعيين
        self.reset_btn = QPushButton("🔄 إعادة تعيين")
        self.reset_btn.setStyleSheet("""
            QPushButton {
                font-size: 14px;
                font-weight: bold;
                color: white;
                background-color: #dc3545;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                min-width: 140px;
                min-height: 45px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:pressed {
                background-color: #bd2130;
            }
        """)

        # زر تصدير الإعدادات
        self.export_settings_btn = QPushButton("📤 تصدير")
        self.export_settings_btn.setStyleSheet("""
            QPushButton {
                font-size: 14px;
                font-weight: bold;
                color: white;
                background-color: #007bff;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                min-width: 120px;
                min-height: 45px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
        """)

        # زر استيراد الإعدادات
        self.import_settings_btn = QPushButton("📥 استيراد")
        self.import_settings_btn.setStyleSheet("""
            QPushButton {
                font-size: 14px;
                font-weight: bold;
                color: white;
                background-color: #6f42c1;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                min-width: 120px;
                min-height: 45px;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
            QPushButton:pressed {
                background-color: #4c2a85;
            }
        """)

        # ترتيب الأزرار
        buttons_layout.addWidget(self.save_all_btn)
        buttons_layout.addWidget(self.reset_btn)
        buttons_layout.addWidget(self.export_settings_btn)
        buttons_layout.addWidget(self.import_settings_btn)
        buttons_layout.addStretch()

        layout.addWidget(buttons_frame)
        
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.save_all_btn.clicked.connect(self.save_all_settings)
        self.reset_btn.clicked.connect(self.reset_settings)
        self.export_settings_btn.clicked.connect(self.export_settings)
        self.import_settings_btn.clicked.connect(self.import_settings)
        
    def on_theme_changed(self):
        """معالج تغيير الثيم"""
        theme_id = get_combobox_value(self.theme_combo)
        if theme_id:
            theme_manager.set_theme(theme_id)

    def toggle_theme(self):
        """تبديل الثيم"""
        new_theme = theme_manager.toggle_theme()
        set_combobox_value(self.theme_combo, new_theme)

    def on_font_size_changed(self, value):
        """معالج تغيير حجم الخط"""
        self.font_size_label.setText(str(value))

        # استخدام مدير الخطوط المحسن
        try:
            from ..utils.font_manager import get_font_manager
            font_manager = get_font_manager()

            # تطبيق حجم الخط الجديد
            font_manager.set_font_size(value)

            # حفظ الإعداد
            font_manager.save_font_size_to_config(value)

            # إشعار بالتغيير
            show_message(self, "تم التحديث", f"✅ تم تحديث حجم الخط إلى {value} نقطة", "information")
        except Exception as e:
            show_message(self, "خطأ", f"❌ فشل في تحديث حجم الخط: {e}", "error")

    def save_all_settings(self):
        """حفظ جميع الإعدادات"""
        try:
            # حفظ إعدادات الثيم
            theme_id = get_combobox_value(self.theme_combo)
            if theme_id:
                theme_manager.set_theme(theme_id)

            # حفظ إعدادات الخط
            font_size = self.font_size_slider.value()
            from ..utils.font_manager import get_font_manager
            font_manager = get_font_manager()
            font_manager.save_font_size_to_config(font_size)

            show_message(self, "نجح", "✅ تم حفظ جميع الإعدادات بنجاح", "information")
        except Exception as e:
            show_message(self, "خطأ", f"❌ فشل في حفظ الإعدادات: {e}", "error")

    def reset_settings(self):
        """إعادة تعيين الإعدادات"""
        if show_confirmation(
            self,
            "تأكيد إعادة التعيين",
            "⚠️ هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟\n\nسيتم فقدان جميع التخصيصات الحالية."
        ):
            try:
                # إعادة تعيين الثيم
                theme_manager.reset_to_default()

                # إعادة تعيين حجم الخط
                self.font_size_slider.setValue(12)
                self.font_size_label.setText("12")

                # إعادة تعيين خانات الاختيار
                self.startup_checkbox.setChecked(False)
                self.minimize_to_tray_checkbox.setChecked(False)
                self.auto_save_checkbox.setChecked(False)
                self.auto_save_interval_spin.setValue(5)
                self.session_timeout_spin.setValue(60)
                self.log_activities_checkbox.setChecked(True)
                self.show_notifications_checkbox.setChecked(True)
                self.sound_notifications_checkbox.setChecked(False)

                show_message(self, "نجح", "✅ تم إعادة تعيين الإعدادات بنجاح", "information")
            except Exception as e:
                show_message(self, "خطأ", f"❌ فشل في إعادة تعيين الإعدادات: {e}", "error")

    def export_settings(self):
        """تصدير الإعدادات"""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "تصدير الإعدادات",
            "hr_settings.json",
            "ملفات JSON (*.json);;جميع الملفات (*)"
        )

        if file_path:
            try:
                import json

                # جمع جميع الإعدادات
                settings = {
                    "theme": get_combobox_value(self.theme_combo),
                    "font_size": self.font_size_slider.value(),
                    "language": get_combobox_value(self.language_combo),
                    "date_format": get_combobox_value(self.date_format_combo),
                    "startup": self.startup_checkbox.isChecked(),
                    "minimize_to_tray": self.minimize_to_tray_checkbox.isChecked(),
                    "auto_save": self.auto_save_checkbox.isChecked(),
                    "auto_save_interval": self.auto_save_interval_spin.value(),
                    "session_timeout": self.session_timeout_spin.value(),
                    "log_activities": self.log_activities_checkbox.isChecked(),
                    "show_notifications": self.show_notifications_checkbox.isChecked(),
                    "sound_notifications": self.sound_notifications_checkbox.isChecked(),
                    "cache_enabled": self.cache_enabled_checkbox.isChecked(),
                    "preload_data": self.preload_data_checkbox.isChecked(),
                    "cache_size": self.cache_size_spin.value()
                }

                # حفظ الملف
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(settings, f, ensure_ascii=False, indent=2)

                show_message(self, "نجح", "✅ تم تصدير الإعدادات بنجاح", "information")
            except Exception as e:
                show_message(self, "خطأ", f"❌ فشل في تصدير الإعدادات: {e}", "error")

    def import_settings(self):
        """استيراد الإعدادات"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "استيراد الإعدادات",
            "",
            "ملفات JSON (*.json);;جميع الملفات (*)"
        )

        if file_path:
            try:
                import json

                # قراءة الملف
                with open(file_path, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                # تطبيق الإعدادات
                if "theme" in settings:
                    set_combobox_value(self.theme_combo, settings["theme"])
                    theme_manager.set_theme(settings["theme"])

                if "font_size" in settings:
                    self.font_size_slider.setValue(settings["font_size"])
                    self.font_size_label.setText(str(settings["font_size"]))

                if "language" in settings:
                    set_combobox_value(self.language_combo, settings["language"])

                if "date_format" in settings:
                    set_combobox_value(self.date_format_combo, settings["date_format"])

                # تطبيق خانات الاختيار
                checkbox_settings = [
                    ("startup", self.startup_checkbox),
                    ("minimize_to_tray", self.minimize_to_tray_checkbox),
                    ("auto_save", self.auto_save_checkbox),
                    ("log_activities", self.log_activities_checkbox),
                    ("show_notifications", self.show_notifications_checkbox),
                    ("sound_notifications", self.sound_notifications_checkbox),
                    ("cache_enabled", self.cache_enabled_checkbox),
                    ("preload_data", self.preload_data_checkbox)
                ]

                for key, checkbox in checkbox_settings:
                    if key in settings:
                        checkbox.setChecked(settings[key])

                # تطبيق القيم الرقمية
                if "auto_save_interval" in settings:
                    self.auto_save_interval_spin.setValue(settings["auto_save_interval"])

                if "session_timeout" in settings:
                    self.session_timeout_spin.setValue(settings["session_timeout"])

                if "cache_size" in settings:
                    self.cache_size_spin.setValue(settings["cache_size"])

                show_message(self, "نجح", "✅ تم استيراد الإعدادات بنجاح", "information")
            except Exception as e:
                show_message(self, "خطأ", f"❌ فشل في استيراد الإعدادات: {e}", "error")
