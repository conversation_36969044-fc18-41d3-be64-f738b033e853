/* 
ملف الأنماط الرئيسي
Main Stylesheet
*/

/* الألوان الأساسية */
* {
    color: #2c3e50;
    font-family: "Aria<PERSON>", "<PERSON><PERSON><PERSON>", sans-serif;
}

/* النافذة الرئيسية */
QMainWindow {
    background-color: #ecf0f1;
}

/* شريط العنوان المحسن */
QFrame#header {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #4a90e2, stop: 1 #357abd);
    border-bottom: 3px solid #2c5aa0;
    min-height: 80px;
    max-height: 80px;
}

QLabel#app_icon {
    color: #ffffff;
    font-size: 24px;
    margin-right: 10px;
}

/* العناوين المحسنة */
QLabel#app_title {
    color: #ffffff;
    font-size: 20px;
    font-weight: bold;
    margin-left: 10px;
}

QLabel#window_title {
    color: #2c3e50;
    font-size: 18px;
    font-weight: bold;
    margin: 15px 0;
    text-align: center;
}

QLabel#section_title {
    color: #34495e;
    font-size: 16px;
    font-weight: bold;
    margin: 10px 0;
}

QLabel#user_icon {
    color: #ffffff;
    font-size: 18px;
    margin-right: 8px;
}

QLabel#user_info {
    color: #ffffff;
    font-size: 14px;
    margin-right: 15px;
}

QPushButton#settings_btn {
    background-color: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    color: #ffffff;
    font-size: 16px;
    min-width: 40px;
    max-width: 40px;
    min-height: 40px;
    max-height: 40px;
}

QPushButton#settings_btn:hover {
    background-color: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

QPushButton#settings_btn:pressed {
    background-color: rgba(255, 255, 255, 0.1);
}

/* الشريط الجانبي المحسن */
QWidget#sidebar {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #f8f9fa, stop: 1 #e9ecef);
    border-right: 3px solid #dee2e6;
    min-width: 280px;
    max-width: 280px;
    padding: 10px;
}

QLabel#sidebar_section_title {
    color: #495057;
    font-weight: bold;
    font-size: 18px;
    padding: 15px 0px;
    text-align: center;
}

QFrame#sidebar_separator {
    color: #dee2e6;
    background-color: #dee2e6;
    height: 2px;
}

/* أزرار الشريط الجانبي المحسنة والمميزة */
QPushButton#sidebar_button {
    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                              stop: 0 #ffffff, stop: 1 #f8f9fa);
    color: #495057;
    border: 2px solid #dee2e6;
    text-align: center;
    padding: 15px 10px;
    margin: 8px 12px;
    border-radius: 20px;
    font-size: 24px;
    font-weight: bold;
    min-height: 80px;
    max-height: 80px;
    min-width: 200px;
}

QPushButton#sidebar_button:hover {
    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                              stop: 0 #007bff, stop: 1 #0056b3);
    color: #ffffff;
    border: 2px solid #0056b3;
}

QPushButton#sidebar_button:checked {
    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                              stop: 0 #28a745, stop: 1 #1e7e34);
    color: #ffffff;
    border: 3px solid #1e7e34;
    font-weight: 900;
}

QPushButton#sidebar_button:pressed {
    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                              stop: 0 #dc3545, stop: 1 #c82333);
    color: #ffffff;
    border: 2px solid #c82333;
}

/* منطقة المحتوى */
QWidget#content_area {
    background-color: #ffffff;
    border: 1px solid #bdc3c7;
}

/* لوحة التحكم المحسنة */
QWidget#dashboard {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #f8f9fa, stop: 1 #ffffff);
}

/* الهيدر المحسن */
QFrame#dashboard_header_frame {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #4a90e2, stop: 1 #357abd);
    border: none;
    border-radius: 15px;
    margin: 10px;
}

QLabel#dashboard_main_title {
    color: #ffffff;
    font-size: 18px;
    font-weight: bold;
    padding: 8px;
    letter-spacing: 1px;
}

QLabel#dashboard_subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: 500;
    padding: 5px;
    letter-spacing: 0.5px;
}

/* إطار الإحصائيات */
QFrame#statistics_frame {
    background-color: #ffffff;
    border: 2px solid #e9ecef;
    border-radius: 15px;
    margin: 10px;
}

QLabel#statistics_section_title {
    color: #2c3e50;
    font-size: 18px;
    font-weight: bold;
    padding: 12px;
    letter-spacing: 1px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* إطار الإجراءات السريعة */
QFrame#quick_actions_frame {
    background-color: #ffffff;
    border: 2px solid #e9ecef;
    border-radius: 15px;
    margin: 10px;
}

QLabel#quick_actions_section_title {
    color: #2c3e50;
    font-size: 18px;
    font-weight: bold;
    padding: 12px;
    letter-spacing: 1px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* مكونات الرسومات البيانية */
QFrame#statistics_chart_widget {
    background-color: #ffffff;
    border: 2px solid #e9ecef;
    border-radius: 15px;
    margin: 10px;
}

QLabel#chart_section_title {
    color: #2c3e50;
    font-size: 18px;
    font-weight: bold;
    padding: 12px;
    letter-spacing: 1px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* الشريط العلوي النظيف */
QFrame#clean_header {
    background-color: #ffffff;
    border-bottom: 3px solid #3498db;
    margin: 0px;
    padding: 0px;
}

/* العنوان الرئيسي النظيف */
QLabel#clean_main_title {
    font-size: 20px;
    font-weight: bold;
    color: #2c3e50;
    letter-spacing: 1px;
    padding: 5px 0;
}

/* العنوان الفرعي النظيف */
QLabel#clean_subtitle {
    font-size: 20px;
    font-weight: 500;
    color: #7f8c8d;
    padding: 3px 0;
}

/* بطاقة المستخدم النظيفة */
QFrame#clean_user_card {
    background-color: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    margin: 2px;
}

/* أيقونة المستخدم النظيفة */
QLabel#clean_user_icon {
    font-size: 20px;
    background-color: #3498db;
    color: white;
    border-radius: 17px;
    padding: 6px;
}

/* تسمية الترحيب النظيفة */
QLabel#clean_welcome_label {
    font-size: 20px;
    font-weight: 500;
    color: #6c757d;
    margin: 0px;
    padding: 1px 0;
}

/* اسم المستخدم النظيف */
QLabel#clean_user_name {
    font-size: 20px;
    font-weight: bold;
    color: #2c3e50;
    margin: 0px;
    padding: 1px 0;
}

/* زر الإعدادات النظيف */
QPushButton#clean_settings_btn {
    background-color: #ffffff;
    border: 2px solid #dee2e6;
    border-radius: 27px;
    padding: 7px;
}

QPushButton#clean_settings_btn:hover {
    background-color: #f8f9fa;
    border: 2px solid #3498db;
}

QPushButton#clean_settings_btn:pressed {
    background-color: #e9ecef;
    border: 2px solid #2980b9;
}

QLabel#section_title {
    color: #34495e;
    font-size: 16px;
    font-weight: bold;
    padding: 15px 0px 10px 0px;
}

/* البطاقات الإحصائية المحسنة */
QFrame#stat_card_blue {
    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                stop: 0 #3498db, stop: 1 #2980b9);
    border: none;
    border-radius: 15px;
    color: #ffffff;
    min-height: 180px;
    min-width: 280px;
}

QFrame#stat_card_green {
    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                stop: 0 #27ae60, stop: 1 #229954);
    border: none;
    border-radius: 15px;
    color: #ffffff;
    min-height: 180px;
    min-width: 280px;
}

QFrame#stat_card_orange {
    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                stop: 0 #f39c12, stop: 1 #e67e22);
    border: none;
    border-radius: 15px;
    color: #ffffff;
    min-height: 180px;
    min-width: 280px;
}

QFrame#stat_card_red {
    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                stop: 0 #e74c3c, stop: 1 #c0392b);
    border: none;
    border-radius: 15px;
    color: #ffffff;
    min-height: 180px;
    min-width: 280px;
}

QFrame#stat_card_purple {
    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                stop: 0 #9b59b6, stop: 1 #8e44ad);
    border: none;
    border-radius: 15px;
    color: #ffffff;
    min-height: 180px;
    min-width: 280px;
}

QFrame#stat_card_teal {
    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                stop: 0 #1abc9c, stop: 1 #16a085);
    border: none;
    border-radius: 15px;
    color: #ffffff;
    min-height: 180px;
    min-width: 280px;
}

QLabel#stat_card_title {
    color: rgba(255, 255, 255, 0.95);
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    letter-spacing: 0.8px;
    line-height: 1.4;
}

QLabel#stat_card_value {
    color: #ffffff;
    font-size: 28px;
    font-weight: 900;
    text-align: center;
    letter-spacing: 1px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

QLabel#stat_card_subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    letter-spacing: 0.5px;
    line-height: 1.3;
}

/* أزرار الإجراءات السريعة */
QPushButton#quick_action_button {
    background-color: #3498db;
    color: #ffffff;
    border: none;
    border-radius: 5px;
    padding: 10px 20px;
    font-size: 11px;
    font-weight: bold;
}

QPushButton#quick_action_button:hover {
    background-color: #2980b9;
}

QPushButton#quick_action_button:pressed {
    background-color: #21618c;
}

/* شريط التمرير */
QScrollBar:vertical {
    background-color: #ecf0f1;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #bdc3c7;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #95a5a6;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    border: none;
    background: none;
}

/* الجداول */
QTableWidget {
    background-color: #ffffff;
    alternate-background-color: #f8f9fa;
    selection-background-color: #3498db;
    selection-color: #ffffff;
    border: 1px solid #bdc3c7;
    gridline-color: #ecf0f1;
}

QTableWidget::item {
    padding: 8px;
    border-bottom: 1px solid #ecf0f1;
}

QTableWidget::item:selected {
    background-color: #3498db;
    color: #ffffff;
}

QHeaderView::section {
    background-color: #34495e;
    color: #ffffff;
    padding: 12px;
    border: none;
    font-weight: bold;
    font-size: 14px;
}

/* حقول الإدخال */
QLineEdit {
    background-color: #ffffff;
    border: 2px solid #bdc3c7;
    border-radius: 5px;
    padding: 8px;
    font-size: 11px;
}

QLineEdit:focus {
    border-color: #3498db;
}

/* القوائم المنسدلة */
QComboBox {
    background-color: #ffffff;
    border: 2px solid #bdc3c7;
    border-radius: 5px;
    padding: 8px;
    font-size: 11px;
}

QComboBox:focus {
    border-color: #3498db;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: url(down_arrow.png);
    width: 12px;
    height: 12px;
}

/* حقول التاريخ */
QDateEdit {
    background-color: #ffffff;
    border: 2px solid #bdc3c7;
    border-radius: 5px;
    padding: 8px;
    font-size: 11px;
}

QDateEdit:focus {
    border-color: #3498db;
}

/* الأزرار العامة */
QPushButton {
    background-color: #3498db;
    color: #ffffff;
    border: none;
    border-radius: 5px;
    padding: 10px 20px;
    font-size: 11px;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #2980b9;
}

QPushButton:pressed {
    background-color: #21618c;
}

QPushButton:disabled {
    background-color: #bdc3c7;
    color: #7f8c8d;
}

/* أزرار الخطر */
QPushButton#danger_button {
    background-color: #e74c3c;
}

QPushButton#danger_button:hover {
    background-color: #c0392b;
}

/* أزرار النجاح */
QPushButton#success_button {
    background-color: #27ae60;
}

QPushButton#success_button:hover {
    background-color: #229954;
}

/* مجموعات العناصر */
QGroupBox {
    font-weight: bold;
    border: 2px solid #bdc3c7;
    border-radius: 5px;
    margin-top: 1ex;
    color: #2c3e50;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 8px 0 8px;
    color: #2c3e50;
    font-size: 16px;
    font-weight: bold;
}

/* مربعات النص */
QTextEdit {
    background-color: #ffffff;
    border: 2px solid #bdc3c7;
    border-radius: 5px;
    padding: 8px;
    font-size: 11px;
    color: #2c3e50;
}

QTextEdit:focus {
    border-color: #3498db;
}

/* شريط التقدم */
QProgressBar {
    border: 2px solid #bdc3c7;
    border-radius: 5px;
    text-align: center;
    color: #2c3e50;
    background-color: #ecf0f1;
}

QProgressBar::chunk {
    background-color: #3498db;
    border-radius: 3px;
}

/* خانات الاختيار */
QCheckBox {
    color: #2c3e50;
}

QCheckBox::indicator {
    width: 18px;
    height: 18px;
}

QCheckBox::indicator:unchecked {
    border: 2px solid #bdc3c7;
    background-color: #ffffff;
    border-radius: 3px;
}

QCheckBox::indicator:checked {
    border: 2px solid #3498db;
    background-color: #3498db;
    border-radius: 3px;
}

/* أزرار الراديو */
QRadioButton {
    color: #2c3e50;
}

QRadioButton::indicator {
    width: 18px;
    height: 18px;
}

QRadioButton::indicator:unchecked {
    border: 2px solid #bdc3c7;
    background-color: #ffffff;
    border-radius: 9px;
}

QRadioButton::indicator:checked {
    border: 2px solid #3498db;
    background-color: #3498db;
    border-radius: 9px;
}

/* التبويبات */
QTabWidget::pane {
    border: 1px solid #bdc3c7;
    background-color: #ffffff;
}

QTabBar::tab {
    background-color: #ecf0f1;
    color: #2c3e50;
    padding: 12px 20px;
    margin-right: 2px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    font-size: 14px;
    font-weight: 600;
}

QTabBar::tab:selected {
    background-color: #3498db;
    color: #ffffff;
}

QTabBar::tab:hover {
    background-color: #bdc3c7;
}

/* شريط الحالة */
QStatusBar {
    background-color: #ecf0f1;
    color: #2c3e50;
    border-top: 1px solid #bdc3c7;
}

/* القوائم */
QMenuBar {
    background-color: #34495e;
    color: #ffffff;
}

QMenuBar::item {
    background-color: transparent;
    padding: 4px 8px;
}

QMenuBar::item:selected {
    background-color: #3498db;
}

QMenu {
    background-color: #ffffff;
    color: #2c3e50;
    border: 1px solid #bdc3c7;
}

QMenu::item {
    padding: 4px 16px;
}

QMenu::item:selected {
    background-color: #3498db;
    color: #ffffff;
}

/* تنسيقات خاصة لنافذة إضافة الموظف */
QDialog#EmployeeForm {
    background-color: #f5f5f5;
    border: 2px solid #667eea;
    border-radius: 16px;
}

QDialog#EmployeeForm QFrame {
    background-color: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 12px;
}

QDialog#EmployeeForm QGroupBox {
    background-color: white;
    border: 2px solid #3498db;
    border-radius: 12px;
    color: #2c3e50;
    font-weight: bold;
    font-size: 16px;
    margin-top: 10px;
    padding-top: 20px;
}

QDialog#EmployeeForm QGroupBox::title {
    subcontrol-origin: margin;
    left: 20px;
    padding: 8px 15px;
    background-color: #3498db;
    color: white;
    border-radius: 8px;
    font-size: 15px;
    font-weight: bold;
}

QDialog#EmployeeForm QLineEdit {
    background-color: #ffffff;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 14px;
    color: #2d3436;
    font-weight: 500;
    min-height: 25px;
    min-width: 250px;
}

QDialog#EmployeeForm QLineEdit:focus {
    border-color: #3498db;
    background-color: #f8f9ff;
    outline: none;
}

QDialog#EmployeeForm QLineEdit:hover {
    border-color: #74b9ff;
    background-color: #fdfdff;
}

QDialog#EmployeeForm QDateEdit {
    background-color: #ffffff;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 14px;
    color: #2d3436;
    font-weight: 500;
    min-height: 25px;
    min-width: 250px;
}

QDialog#EmployeeForm QDateEdit:focus {
    border-color: #3498db;
    background-color: #f8f9ff;
    outline: none;
}

QDialog#EmployeeForm QDateEdit:hover {
    border-color: #74b9ff;
    background-color: #fdfdff;
}

QDialog#EmployeeForm QComboBox {
    background-color: #ffffff;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 14px;
    color: #2d3436;
    font-weight: 500;
    min-height: 25px;
    min-width: 250px;
}

QDialog#EmployeeForm QComboBox:focus {
    border-color: #27ae60;
    background-color: #f0fff4;
    outline: none;
}

QDialog#EmployeeForm QComboBox:hover {
    border-color: #55a3ff;
    background-color: #fdfdff;
}

QDialog#EmployeeForm QComboBox::drop-down {
    border: none;
    width: 35px;
    background-color: transparent;
}

QDialog#EmployeeForm QComboBox::down-arrow {
    image: none;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid #27ae60;
    margin-right: 12px;
}

QDialog#EmployeeForm QComboBox QAbstractItemView {
    background-color: #ffffff;
    color: #2d3436;
    selection-background-color: #27ae60;
    selection-color: #ffffff;
    border: 2px solid #e9ecef;
    border-radius: 8px;
}

QDialog#EmployeeForm QTextEdit {
    background-color: #ffffff;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 14px;
    color: #2d3436;
    font-weight: 500;
}

QDialog#EmployeeForm QTextEdit:focus {
    border-color: #3498db;
    background-color: #f8f9ff;
    outline: none;
}

QDialog#EmployeeForm QTextEdit:hover {
    border-color: #74b9ff;
    background-color: #fdfdff;
}

QDialog#EmployeeForm QLabel {
    color: #2d3436;
    font-size: 14px;
    font-weight: 600;
    padding: 8px 5px;
    min-width: 120px;
}

/* تنسيقات خاصة لمجموعة الرقم الوظيفي */
QDialog#EmployeeForm QGroupBox[title*="الرقم الوظيفي"] {
    background-color: white;
    border: 2px solid #e74c3c;
    border-radius: 12px;
    color: #2c3e50;
    font-weight: bold;
    font-size: 16px;
    margin-top: 10px;
    padding-top: 20px;
}

QDialog#EmployeeForm QGroupBox[title*="الرقم الوظيفي"]::title {
    subcontrol-origin: margin;
    left: 20px;
    padding: 8px 15px;
    background-color: #e74c3c;
    color: white;
    border-radius: 8px;
    font-size: 15px;
    font-weight: bold;
}

/* تنسيق عرض الرقم الوظيفي */
QDialog#EmployeeForm QGroupBox[title*="الرقم الوظيفي"] QLabel {
    color: #2d3436;
    font-size: 14px;
    font-weight: 600;
    padding: 8px 5px;
    text-align: center;
}

/* تنسيق خاص لعرض الرقم */
QDialog#EmployeeForm QGroupBox[title*="الرقم الوظيفي"] QLabel[text*="EMP"] {
    color: #e74c3c;
    font-size: 18px;
    font-weight: bold;
    background-color: #fdf2f2;
    border: 2px solid #e74c3c;
    border-radius: 8px;
    padding: 15px 10px;
    margin: 5px;
    text-align: center;
}

/* تنسيقات خاصة لمجموعة المعلومات الوظيفية */
QDialog#EmployeeForm QGroupBox[title*="المعلومات الوظيفية"] {
    background-color: white;
    border: 2px solid #27ae60;
    border-radius: 12px;
    color: #2c3e50;
    font-weight: bold;
    font-size: 16px;
    margin-top: 10px;
    padding-top: 20px;
}

QDialog#EmployeeForm QGroupBox[title*="المعلومات الوظيفية"]::title {
    subcontrol-origin: margin;
    left: 20px;
    padding: 8px 15px;
    background-color: #27ae60;
    color: white;
    border-radius: 8px;
    font-size: 15px;
    font-weight: bold;
}

/* تنسيقات خاصة لمجموعة المعلومات الشخصية */
QDialog#EmployeeForm QGroupBox[title*="المعلومات الشخصية"] {
    background-color: white;
    border: 2px solid #3498db;
    border-radius: 12px;
    color: #2c3e50;
    font-weight: bold;
    font-size: 16px;
    margin-top: 10px;
    padding-top: 20px;
}

QDialog#EmployeeForm QGroupBox[title*="المعلومات الشخصية"]::title {
    subcontrol-origin: margin;
    left: 20px;
    padding: 8px 15px;
    background-color: #3498db;
    color: white;
    border-radius: 8px;
    font-size: 15px;
    font-weight: bold;
}

/* تنسيقات خاصة للأزرار في نافذة الموظف */
QDialog#EmployeeForm QPushButton[text*="حفظ"] {
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: 10px;
    padding: 15px 25px;
    font-size: 16px;
    font-weight: bold;
    min-width: 160px;
    min-height: 50px;
}

QDialog#EmployeeForm QPushButton[text*="حفظ"]:hover {
    background-color: #218838;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

QDialog#EmployeeForm QPushButton[text*="حفظ"]:pressed {
    background-color: #1e7e34;
    transform: translateY(0px);
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

QDialog#EmployeeForm QPushButton[text*="مسح"] {
    background-color: #ffc107;
    color: white;
    border: none;
    border-radius: 10px;
    padding: 15px 25px;
    font-size: 16px;
    font-weight: bold;
    min-width: 140px;
    min-height: 50px;
}

QDialog#EmployeeForm QPushButton[text*="مسح"]:hover {
    background-color: #e0a800;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(255, 193, 7, 0.3);
}

QDialog#EmployeeForm QPushButton[text*="مسح"]:pressed {
    background-color: #d39e00;
    transform: translateY(0px);
    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
}

QDialog#EmployeeForm QPushButton[text*="إلغاء"] {
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 10px;
    padding: 15px 25px;
    font-size: 16px;
    font-weight: bold;
    min-width: 130px;
    min-height: 50px;
}

QDialog#EmployeeForm QPushButton[text*="إلغاء"]:hover {
    background-color: #c82333;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

QDialog#EmployeeForm QPushButton[text*="إلغاء"]:pressed {
    background-color: #bd2130;
    transform: translateY(0px);
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

/* تنسيق إطار الأزرار */
QDialog#EmployeeForm QFrame {
    background-color: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    margin: 15px 0px;
}

/* تنسيقات الرأس في نافذة الموظف */
QDialog#EmployeeForm QFrame#header_frame {
    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                stop: 0 #667eea, stop: 1 #764ba2);
    border: none;
    border-radius: 16px;
    margin: 12px;
    padding: 20px;
}

QDialog#EmployeeForm QLabel#title_label {
    color: #ffffff;
    font-size: 22px;
    font-weight: bold;
    padding: 8px;
    margin: 0px;
    background-color: transparent;
}

QDialog#EmployeeForm QLabel#subtitle_label {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: 600;
    background-color: #f3e5f5;
    padding: 6px 15px;
    border-radius: 6px;
    border-left: 3px solid #9c27b0;
    margin: 2px;
}

/* تحسين أحجام الخطوط في النافذة */
QDialog#EmployeeForm {
    font-size: 14px;
}

QDialog#EmployeeForm QLineEdit {
    font-size: 16px;
    padding: 12px 15px;
    min-height: 25px;
    min-width: 500px;
}

QDialog#EmployeeForm QDateEdit {
    font-size: 14px;
    padding: 12px 15px;
    min-height: 25px;
    min-width: 250px;
}

QDialog#EmployeeForm QComboBox {
    font-size: 14px;
    padding: 12px 15px;
    min-height: 25px;
    min-width: 250px;
}

QDialog#EmployeeForm QTextEdit {
    font-size: 14px;
    padding: 12px 15px;
}

/* تنسيق خاص لحقل الاسم الكامل المكبر */
QDialog#EmployeeForm QLineEdit[placeholderText*="الاسم الثلاثي"] {
    font-size: 16px;
    min-width: 500px;
    font-weight: 500;
}
